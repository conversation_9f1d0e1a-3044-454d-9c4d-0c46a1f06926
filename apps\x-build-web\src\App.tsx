import { ProLayout, ProSettings } from '@ant-design/pro-components';
import { QueryClientProvider } from '@tanstack/react-query';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
// import { queryClient } from '@web/utils/tRPC';
import { BranchesOutlined, DatabaseOutlined, ScheduleOutlined } from '@ant-design/icons';
import { StyleProvider } from '@ant-design/cssinjs';
import { ConfigProvider } from 'antd';
import { useMemo} from 'react';
import { queryClient } from './utils/tRPC';


const config = {
  path: '/',
  name: '首页',
  routes: [
    {
      path: '/workspace',
      name: '工作区',
      routes: [
        {
          path: ':id',
          name: '详情',
          hideInMenu: true,
          routes: [
            {
              path: 'addView',
              name: '新建视图',
              icon: <BranchesOutlined />,
              routes: [],
              hideInMenu: true,
            },
          ]
        },
      ],
    },
    {
      path: '/publicWorkspace',
      name: '公共工作区',
      // icon: <CrownFilled />,
      // access: 'canAdmin',
      routes: [
        {
          path: 'upload',
          name: '上传文件',
          hideInMenu: true,
          routes: []
        },
        {
          path: 'datasets',
          name: '数据集',
          icon: <DatabaseOutlined />,
          routes: []
          // hideInMenu: true,
        },
        {
          path: 'operation',
          name: '操作面板',
          icon: <ScheduleOutlined />,
          routes: []
        },
        {
          path: 'logic',
          name: '业务逻辑',
          icon: <BranchesOutlined />,
          routes: []
        },
        {
          path: 'addView',
          name: '新建视图',
          routes: [],
          hideInMenu: true,
        },
      ]
    },

  ],
}
const settings: Partial<ProSettings> | undefined = {
  fixSiderbar: true,
  layout: 'mix',
  splitMenus: true,
  contentWidth: 'Fluid',
};

const hideMenuPaths = ['/publicWorkspace/addView','/publicWorkspace/operation/pageBuilder'];
// const hideHeaderPaths=['workspace','publicWorkspace/addView']
// function makeQueryClient() {
//   return new QueryClient({
//     defaultOptions: {
//       queries: {
//         // With SSR, we usually want to set some default staleTime
//         // above 0 to avoid refetching immediately on the client
//         staleTime: 60 * 1000,
//       },
//     },
//   });
// }
// let browserQueryClient: QueryClient | undefined = undefined;
// function getQueryClient() {
//   if (typeof window === 'undefined') {
//     // Server: always make a new query client
//     return makeQueryClient();
//   } else {
//     // Browser: make a new query client if we don't already have one
//     // This is very important, so we don't re-make a new client if React
//     // suspends during the initial render. This may not be needed if we
//     // have a suspense boundary BELOW the creation of the query client
//     if (!browserQueryClient) browserQueryClient = makeQueryClient();
//     return browserQueryClient;
//   }
// }

function App() {
  const location = useLocation();
  const navigate = useNavigate();
  const shouldHideMenu = useMemo(() => {
    return hideMenuPaths.includes(location.pathname);
  }, [location.pathname]);
  const workspaceHeader = useMemo(() => {
    return location.pathname.split('/').length > 2 && location.pathname.split('/').includes('workspace')
  }, [location.pathname]);
  // const queryClient = getQueryClient();
  // const [trpcClient] = useState(() => createTRPCClientInstance());
  return (
    <StyleProvider>
      <ConfigProvider>
        <QueryClientProvider client={queryClient}>
          {/* <trpc.Provider client={trpcClient} queryClient={queryClient}> */}
            <ProLayout
              title={"X-Build"}
              logo={null}
              // breadcrumbProps={{
              //   items: getBreadcrumbItems(config.routes, location.pathname),
              //   itemRender: (currentRoute, _, routes) => {
              //     const isLast = currentRoute?.path === routes[routes.length - 1]?.path;
              //     // 直接使用面包屑path字段作为跳转URL
              //     return isLast ? (
              //       <span>{currentRoute.title}</span>
              //     ) : (
              //       <Link to={currentRoute.path!}>{currentRoute.title}</Link>
              //     );
              //   },
              //   className: "px-6 color-gray-500"
              // }}
              className='h-screen'
              breadcrumbRender={false}
              siderWidth={150}
              pageTitleRender={false}
              token={{
                bgLayout: '#fff',
                header: {
                  colorBgHeader: '#fff',
                },
                sider: {
                  colorMenuBackground: '#fff',
                },
                pageContainer: {
                  colorBgPageContainer: '#fff',
                  paddingBlockPageContainerContent: 0,
                  paddingInlinePageContainerContent: 0,
                },
              }}
              route={config}
              location={{
                pathname: location.pathname,
              }}
              menu={{
                type: 'group',
              }}
              headerRender={workspaceHeader ? false : undefined}
              menuRender={shouldHideMenu ? false : undefined}
              defaultCollapsed={true}
              breakpoint={false}
              onMenuHeaderClick={() => navigate('/')}
              menuItemRender={(item, dom) => (
                <a
                  onClick={() => {
                    navigate(item.path || '/workspace');
                  }}
                >
                  {dom}
                </a>
              )}
              {...settings}
            >
              <Outlet />
            </ProLayout>
          {/* </trpc.Provider> */}
        </QueryClientProvider>
      </ConfigProvider>
    </StyleProvider>
  )
}

export default App
