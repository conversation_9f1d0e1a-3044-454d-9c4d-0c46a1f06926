import { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, Radio, Button, Tag, message, Checkbox } from 'antd';
import { PlusOutlined, DeleteOutlined, SettingOutlined } from '@ant-design/icons';
import { nanoid } from 'nanoid';
import type { ComponentItem, ComponentType, CreateComponentForm, ComponentParameter } from '../types/component';
import { mockDataSources, mockComponentCategories, getRelatedDataSources, getAvailableParameters } from '../mock/componentData';

const { TextArea } = Input;
const { Option } = Select;

interface CreateComponentModalProps {
  open: boolean;
  onOk: (component: ComponentItem) => void;
  onCancel: () => void;
}

const CreateComponentModal: React.FC<CreateComponentModalProps> = ({
  open,
  onOk,
  onCancel
}) => {
  const [form] = Form.useForm<CreateComponentForm>();
  const [selectedDataSource, setSelectedDataSource] = useState<string>('');
  const [selectedType, setSelectedType] = useState<ComponentType>('chart');
  const [relatedDataSources, setRelatedDataSources] = useState<string[]>([]);
  const [selectedParameters, setSelectedParameters] = useState<ComponentParameter[]>([]);
  const [availableParameters, setAvailableParameters] = useState<ComponentParameter[]>([]);
  const [parameterModalOpen, setParameterModalOpen] = useState<boolean>(false);
  const [tempSelectedParameters, setTempSelectedParameters] = useState<string[]>([]);

  // 监听数据源变化，更新关联数据源
  useEffect(() => {
    if (selectedDataSource) {
      const related = getRelatedDataSources(selectedDataSource);
      const relatedIds = related.map(ds => ds.id);
      setRelatedDataSources(relatedIds);

      // 更新可用参数
      const params = getAvailableParameters(selectedDataSource, relatedIds);
      setAvailableParameters(params);

      // 清空已选参数
      setSelectedParameters([]);
    } else {
      setRelatedDataSources([]);
      setAvailableParameters([]);
      setSelectedParameters([]);
    }
  }, [selectedDataSource]);

  // 处理数据源选择
  const handleDataSourceChange = (value: string) => {
    setSelectedDataSource(value);
    form.setFieldValue('dataSource', value);
  };

  // 处理组件类型选择
  const handleTypeChange = (value: ComponentType) => {
    setSelectedType(value);
    form.setFieldValue('type', value);
  };

  // 添加参数
  const handleAddParameter = (param: ComponentParameter) => {
    const exists = selectedParameters.find(p => p.id === param.id);
    if (!exists) {
      setSelectedParameters(prev => [...prev, param]);
    }
  };

  // 移除参数
  const handleRemoveParameter = (paramId: string) => {
    setSelectedParameters(prev => prev.filter(p => p.id !== paramId));
  };

  // 打开参数选择弹窗
  const handleOpenParameterModal = () => {
    setTempSelectedParameters(selectedParameters.map(p => p.id));
    setParameterModalOpen(true);
  };

  // 参数选择弹窗确认
  const handleParameterModalOk = () => {
    const newSelectedParams = availableParameters.filter(param =>
      tempSelectedParameters.includes(param.id)
    );
    setSelectedParameters(newSelectedParams);
    setParameterModalOpen(false);
  };

  // 参数选择弹窗取消
  const handleParameterModalCancel = () => {
    setParameterModalOpen(false);
    setTempSelectedParameters(selectedParameters.map(p => p.id));
  };

  // 参数选择变化
  const handleParameterSelectionChange = (checkedValues: string[]) => {
    setTempSelectedParameters(checkedValues);
  };

  // 表单提交
  const handleSubmit = () => {
    form.validateFields().then(values => {
      const selectedDataSourceInfo = mockDataSources.find(ds => ds.id === values.dataSource);
      const relatedDataSourcesInfo = mockDataSources.filter(ds => relatedDataSources.includes(ds.id));

      const newComponent: ComponentItem = {
        id: nanoid(),
        name: values.name,
        type: values.type,
        dataSource: values.dataSource,
        dataSourceName: selectedDataSourceInfo?.name,
        relatedDataSources: relatedDataSources,
        relatedDataSourceNames: relatedDataSourcesInfo.map(ds => ds.name),
        parameters: selectedParameters,
        description: values.description,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        config: {}
      };

      onOk(newComponent);
      handleReset();
      message.success('组件创建成功');
    }).catch(error => {
      console.error('表单验证失败:', error);
    });
  };

  // 重置表单
  const handleReset = () => {
    form.resetFields();
    setSelectedDataSource('');
    setSelectedType('chart');
    setRelatedDataSources([]);
    setSelectedParameters([]);
    setAvailableParameters([]);
  };

  // 取消操作
  const handleCancel = () => {
    handleReset();
    onCancel();
  };

  return (
    <Modal
      title="新建组件"
      open={open}
      onOk={handleSubmit}
      onCancel={handleCancel}
      okText="确定"
      cancelText="取消"
      width={600}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          type: 'chart'
        }}
      >
        {/* 组件名称 */}
        <Form.Item
          label="组件名称"
          name="name"
          rules={[
            { required: true, message: '请输入组件名称' },
            { min: 2, max: 50, message: '组件名称长度应在2-50个字符之间' }
          ]}
        >
          <Input placeholder="请输入组件名称" />
        </Form.Item>

        {/* 选择数据源 */}
        <Form.Item
          label="选择数据源"
          name="dataSource"
          rules={[{ required: true, message: '请选择数据源' }]}
        >
          <Select
            placeholder="请选择主数据源"
            onChange={handleDataSourceChange}
            showSearch
            filterOption={(input, option) =>
              (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
            }
          >
            {mockDataSources.map(ds => (
              <Option key={ds.id} value={ds.id}>
                {ds.name} ({ds.type})
              </Option>
            ))}
          </Select>
        </Form.Item>

        {/* 关联数据源 */}
        {relatedDataSources.length > 0 && (
          <Form.Item label="关联数据源">
            <div className="p-3 bg-blue-50 rounded border border-blue-200">
              <div className="text-sm text-blue-700 mb-2 font-medium">
                系统自动识别的关联数据源 ({relatedDataSources.length}个)：
              </div>
              <div className="space-y-2">
                {relatedDataSources.map(dsId => {
                  const ds = mockDataSources.find(d => d.id === dsId);
                  return ds ? (
                    <div key={dsId} className="flex items-center justify-between p-2 bg-white rounded border">
                      <div className="flex items-center gap-2">
                        <Tag color="blue">
                          {ds.type}
                        </Tag>
                        <span className="text-sm font-medium">{ds.name}</span>
                        {ds.description && (
                          <span className="text-xs text-gray-500">({ds.description})</span>
                        )}
                      </div>
                      <div className="text-xs text-gray-400">
                        {ds.fields?.length || 0} 字段
                      </div>
                    </div>
                  ) : null;
                })}
              </div>
              <div className="mt-2 text-xs text-blue-600">
                💡 这些数据源将自动关联到组件中，可用于跨表查询和数据联动
              </div>
            </div>
          </Form.Item>
        )}

        {/* 组件类型 */}
        <Form.Item
          label="组件类型"
          name="type"
          rules={[{ required: true, message: '请选择组件类型' }]}
        >
          <Radio.Group onChange={(e) => handleTypeChange(e.target.value)}>
            {mockComponentCategories.map(category => (
              <Radio.Button key={category.type} value={category.type}>
                {category.label}
              </Radio.Button>
            ))}
          </Radio.Group>
        </Form.Item>

        {/* 组件入参 */}
        <Form.Item label="组件入参">
          <div className="space-y-3">
            {/* 参数选择操作区 */}
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                已选择 {selectedParameters.length} 个参数
                {availableParameters.length > 0 && (
                  <span className="text-gray-400">，共 {availableParameters.length} 个可选</span>
                )}
              </div>
              {availableParameters.length > 0 && (
                <Button
                  type="primary"
                  size="small"
                  icon={<SettingOutlined />}
                  onClick={handleOpenParameterModal}
                >
                  批量选择参数
                </Button>
              )}
            </div>

            {/* 已选参数 */}
            {selectedParameters.length > 0 && (
              <div>
                <div className="space-y-1">
                  {selectedParameters.map(param => {
                    // 判断参数来源
                    const isFromMainSource = param.source === selectedDataSource;
                    const sourceInfo = isFromMainSource
                      ? mockDataSources.find(ds => ds.id === selectedDataSource)
                      : mockDataSources.find(ds => relatedDataSources.includes(ds.id));

                    return (
                      <div key={param.id} className="flex items-center justify-between p-2 bg-blue-50 rounded border">
                        <div className="flex items-center gap-2">
                          <Tag color={param.required ? 'red' : 'default'}>
                            {param.type}
                          </Tag>
                          <span className="text-sm font-medium">{param.name}</span>
                          {param.description && (
                            <span className="text-xs text-gray-500">({param.description})</span>
                          )}
                          <Tag color={isFromMainSource ? 'blue' : 'green'} className="text-xs">
                            {isFromMainSource ? '主数据源' : '关联数据源'}
                          </Tag>
                        </div>
                        <Button
                          type="text"
                          size="small"
                          icon={<DeleteOutlined />}
                          onClick={() => handleRemoveParameter(param.id)}
                          className="text-red-500 hover:text-red-700"
                        />
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* 可选参数 */}
            {availableParameters.length > 0 && (
              <div>
                <div className="text-sm text-gray-600 mb-2">可选参数：</div>
                <div className="max-h-32 overflow-y-auto space-y-1">
                  {availableParameters
                    .filter(param => !selectedParameters.find(sp => sp.id === param.id))
                    .map(param => (
                      <div key={param.id} className="flex items-center justify-between p-2 bg-gray-50 rounded border hover:bg-gray-100">
                        <div className="flex items-center gap-2">
                          <Tag color={param.required ? 'red' : 'default'}>
                            {param.type}
                          </Tag>
                          <span className="text-sm">{param.name}</span>
                          {param.description && (
                            <span className="text-xs text-gray-500">({param.description})</span>
                          )}
                        </div>
                        <Button
                          type="text"
                          size="small"
                          icon={<PlusOutlined />}
                          onClick={() => handleAddParameter(param)}
                          className="text-blue-500 hover:text-blue-700"
                        />
                      </div>
                    ))}
                </div>
              </div>
            )}

            {availableParameters.length === 0 && selectedDataSource && (
              <div className="text-center text-gray-500 py-4">
                暂无可用参数
              </div>
            )}
          </div>
        </Form.Item>

        {/* 备注 */}
        <Form.Item
          label="备注"
          name="description"
        >
          <TextArea
            placeholder="请输入组件描述（可选）"
            rows={3}
            maxLength={200}
            showCount
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateComponentModal;
