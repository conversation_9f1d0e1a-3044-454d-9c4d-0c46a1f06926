import { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, Radio, Button, Tag, message, Steps } from 'antd';
import { SettingOutlined, LeftOutlined, RightOutlined } from '@ant-design/icons';
import { nanoid } from 'nanoid';
import type { ComponentItem, ComponentType, CreateComponentForm, ComponentParameter, ChartConfig, CardConfig } from '../types/component';
import { mockDataSources, mockComponentCategories, getRelatedDataSources, getAvailableParameters } from '../mock/componentData';
import ChartConfigStep from './ChartConfigStep';
import CardConfigStep from './CardConfigStep';

const { TextArea } = Input;
const { Option } = Select;

interface CreateComponentModalProps {
  open: boolean;
  onOk: (component: ComponentItem) => void;
  onCancel: () => void;
}

// 步骤定义
const STEPS = [
  {
    title: '基本信息',
    description: '设置组件名称、数据源和类型'
  },
  {
    title: '详细配置',
    description: '配置组件的具体参数和样式'
  }
];

const CreateComponentModal: React.FC<CreateComponentModalProps> = ({
  open,
  onOk,
  onCancel
}) => {
  const [form] = Form.useForm<CreateComponentForm>();
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [selectedDataSource, setSelectedDataSource] = useState<string>('');
  const [selectedType, setSelectedType] = useState<ComponentType>('chart');
  const [relatedDataSources, setRelatedDataSources] = useState<string[]>([]);
  const [selectedParameters, setSelectedParameters] = useState<ComponentParameter[]>([]);
  const [availableParameters, setAvailableParameters] = useState<ComponentParameter[]>([]);


  // 配置步骤状态
  const [chartConfig, setChartConfig] = useState<ChartConfig | null>(null);
  const [cardConfig, setCardConfig] = useState<CardConfig | null>(null);

  // 监听数据源变化，更新关联数据源
  useEffect(() => {
    if (selectedDataSource) {
      const related = getRelatedDataSources(selectedDataSource);
      const relatedIds = related.map(ds => ds.id);
      setRelatedDataSources(relatedIds);

      // 更新可用参数
      const params = getAvailableParameters(selectedDataSource, relatedIds);
      setAvailableParameters(params);

      // 清空已选参数
      setSelectedParameters([]);
    } else {
      setRelatedDataSources([]);
      setAvailableParameters([]);
      setSelectedParameters([]);
    }
  }, [selectedDataSource]);

  // 处理数据源选择
  const handleDataSourceChange = (value: string) => {
    setSelectedDataSource(value);
    form.setFieldValue('dataSource', value);
  };

  // 处理组件类型选择
  const handleTypeChange = (value: ComponentType) => {
    setSelectedType(value);
    form.setFieldValue('type', value);
  };

  // 步骤导航函数
  const handleNext = async () => {
    if (currentStep === 0) {
      // 第一步验证基本信息
      try {
        await form.validateFields(['name', 'dataSource', 'type']);
        setCurrentStep(1);
      } catch (error) {
        console.error('基本信息验证失败:', error);
      }
    }
  };

  const handlePrev = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // 判断是否可以进入下一步
  const canGoNext = () => {
    if (currentStep === 0) {
      const values = form.getFieldsValue(['name', 'dataSource', 'type']);
      return values.name && values.dataSource && values.type;
    }
    return false;
  };

  // 判断是否可以完成创建
  const canComplete = () => {
    if (currentStep === 1) {
      if (selectedType === 'chart') {
        return chartConfig && chartConfig.dimensions.length > 0 && chartConfig.metrics.length > 0;
      } else if (selectedType === 'card') {
        return cardConfig && cardConfig.fields.length > 0;
      }
      // 其他类型暂时允许完成
      return true;
    }
    return false;
  };







  // 表单提交
  const handleSubmit = () => {
    form.validateFields().then(values => {
      const selectedDataSourceInfo = mockDataSources.find(ds => ds.id === values.dataSource);
      const relatedDataSourcesInfo = mockDataSources.filter(ds => relatedDataSources.includes(ds.id));

      // 根据组件类型构建配置
      let componentConfig = {};
      if (selectedType === 'chart' && chartConfig) {
        componentConfig = chartConfig;
      } else if (selectedType === 'card' && cardConfig) {
        componentConfig = cardConfig;
      }

      const newComponent: ComponentItem = {
        id: nanoid(),
        name: values.name,
        type: values.type,
        dataSource: values.dataSource,
        dataSourceName: selectedDataSourceInfo?.name,
        relatedDataSources: relatedDataSources,
        relatedDataSourceNames: relatedDataSourcesInfo.map(ds => ds.name),
        parameters: selectedParameters,
        description: values.description,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        config: componentConfig
      };

      onOk(newComponent);
      handleReset();
      message.success('组件创建成功');
    }).catch(error => {
      console.error('表单验证失败:', error);
    });
  };

  // 重置表单
  const handleReset = () => {
    form.resetFields();
    setCurrentStep(0);
    setSelectedDataSource('');
    setSelectedType('chart');
    setRelatedDataSources([]);
    setSelectedParameters([]);
    setAvailableParameters([]);
    setChartConfig(null);
    setCardConfig(null);
  };

  // 取消操作
  const handleCancel = () => {
    handleReset();
    onCancel();
  };

  // 渲染Modal底部按钮
  const renderFooter = () => {
    const buttons = [];

    // 上一步按钮
    if (currentStep > 0) {
      buttons.push(
        <Button
          key="prev"
          onClick={handlePrev}
          icon={<LeftOutlined />}
          className="hover:bg-gray-50"
        >
          上一步
        </Button>
      );
    }

    // 取消按钮
    buttons.push(
      <Button
        key="cancel"
        onClick={handleCancel}
        className="hover:bg-gray-50"
      >
        取消
      </Button>
    );

    // 下一步/完成按钮
    if (currentStep < STEPS.length - 1) {
      buttons.push(
        <Button
          key="next"
          type="primary"
          onClick={handleNext}
          disabled={!canGoNext()}
          icon={<RightOutlined />}
          className="bg-blue-500 hover:bg-blue-600 border-blue-500 hover:border-blue-600 shadow-sm"
          loading={false}
        >
          下一步
        </Button>
      );
    } else {
      buttons.push(
        <Button
          key="submit"
          type="primary"
          onClick={handleSubmit}
          disabled={!canComplete()}
          className="bg-green-500 hover:bg-green-600 border-green-500 hover:border-green-600 shadow-sm"
          loading={false}
        >
          完成创建
        </Button>
      );
    }

    return buttons;
  };

  return (
    <>
      <Modal
        title={
          <div>
            新建组件
          </div>
        }
        open={open}
        onCancel={handleCancel}
        footer={renderFooter()}
        width={currentStep === 0 ? 800 : 1200}
        style={{
          top: 20,
          maxHeight: 'calc(100vh - 40px)',
          paddingBottom: 0
        }}
        destroyOnHidden
        className="create-component-modal"
        styles={{
          body: {
            maxHeight: 'calc(100vh - 200px)',
            overflowY: 'auto',
            paddingRight: '6px'
          }
        }}
      >
        {/* 步骤导航 */}
        <div className="mb-6 px-4 py-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
          <Steps
            current={currentStep}
            items={STEPS.map((step, index) => ({
              ...step,
              status: currentStep === index ? 'process' : currentStep > index ? 'finish' : 'wait',
              icon: currentStep === index ? <SettingOutlined className="animate-pulse" /> : undefined
            }))}
            size="small"
            className="custom-steps"
          />
        </div>
        {/* 表单内容 */}
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            type: 'chart'
          }}
        >
          {/* 第一步：基本信息 */}
          {currentStep === 0 && (
            <div className="space-y-4 animate-fadeIn">
              {/* 组件名称 */}
              <Form.Item
                label="组件名称"
                name="name"
                rules={[
                  { required: true, message: '请输入组件名称' },
                  { min: 2, max: 50, message: '组件名称长度应在2-50个字符之间' }
                ]}
              >
                <Input placeholder="请输入组件名称" />
              </Form.Item>

              {/* 选择数据源 */}
              <Form.Item
                label="选择数据源"
                name="dataSource"
                rules={[{ required: true, message: '请选择数据源' }]}
              >
                <Select
                  placeholder="请选择主数据源"
                  onChange={handleDataSourceChange}
                  showSearch
                  filterOption={(input, option) =>
                    (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {mockDataSources.map(ds => (
                    <Option key={ds.id} value={ds.id}>
                      {ds.name} ({ds.type})
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              {/* 关联数据源 */}
              {relatedDataSources.length > 0 && (
                <Form.Item label="关联数据源">
                  <div className="flex flex-wrap gap-2">
                    {relatedDataSources.map(dsId => {
                      const ds = mockDataSources.find(d => d.id === dsId);
                      return ds ? (
                        <Tag key={dsId} color="blue">
                          {ds.name}
                        </Tag>
                      ) : null;
                    })}
                  </div>
                </Form.Item>
              )}

              {/* 组件类型 */}
              <Form.Item
                label="组件类型"
                name="type"
                rules={[{ required: true, message: '请选择组件类型' }]}
              >
                <Radio.Group onChange={(e) => handleTypeChange(e.target.value)}>
                  {mockComponentCategories.map(category => (
                    <Radio.Button key={category.type} value={category.type}>
                      {category.label}
                    </Radio.Button>
                  ))}
                </Radio.Group>
              </Form.Item>

              {/* 组件入参 */}
              <Form.Item label="组件入参">
                <div className="space-y-3">
                  {/* 参数选择器 */}
                  <Select
                    mode="multiple"
                    placeholder="选择参数"
                    value={selectedParameters.map(p => p.id)}
                    onChange={(values) => {
                      const newSelectedParams = availableParameters.filter(param =>
                        values.includes(param.id)
                      );
                      setSelectedParameters(newSelectedParams);
                    }}
                    disabled={availableParameters.length === 0}
                    showSearch
                    filterOption={(input, option) =>
                      (option?.label as string)?.toLowerCase().includes(input.toLowerCase())
                    }
                    tagRender={(props) => {
                      const { value, closable, onClose } = props;
                      const param = availableParameters.find(p => p.id === value);
                      const isFromMainSource = param?.source === selectedDataSource;

                      return (
                        <Tag
                          color={isFromMainSource ? 'blue' : 'green'}
                          closable={closable}
                          onClose={onClose}
                          style={{ marginRight: 3 }}
                        >
                          {param?.name}
                        </Tag>
                      );
                    }}
                    options={availableParameters.map(param => ({
                      value: param.id,
                      label: `${param.name} (${param.type})`,
                      disabled: false
                    }))}
                  />

                  {/* 提示信息 */}
                  <div className="text-xs text-gray-500">
                    {availableParameters.length === 0 && selectedDataSource && (
                      "暂无可用参数"
                    )}
                    {!selectedDataSource && (
                      "请先选择数据源"
                    )}
                    {availableParameters.length > 0 && (
                      `点击选择下拉框选择参数（根据当前选择的数据源与关联表显示参数）`
                    )}
                  </div>
                </div>
              </Form.Item>

              {/* 备注 */}
              <Form.Item
                label="备注"
                name="description"
              >
                <TextArea
                  placeholder="请输入组件描述（可选）"
                  rows={3}
                  maxLength={200}
                  showCount
                />
              </Form.Item>
            </div>
          )}

          {/* 第二步：详细配置 */}
          {currentStep === 1 && (
            <div className="space-y-4 animate-fadeIn">
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-100 mb-4">
                <div className="flex items-center gap-2 text-blue-700">
                  <SettingOutlined />
                  <span className="font-medium">
                    {selectedType === 'chart' && '图表组件配置'}
                    {selectedType === 'card' && '卡片组件配置'}
                    {selectedType === 'table' && '表格组件配置'}
                    {selectedType === 'detail' && '详情组件配置'}
                  </span>
                </div>
                <div className="text-sm text-blue-600 mt-1">
                  {selectedType === 'chart' && '配置图表类型、数据字段和样式选项'}
                  {selectedType === 'card' && '配置卡片类型、显示字段和样式选项'}
                  {selectedType === 'table' && '配置表格列、排序和筛选选项'}
                  {selectedType === 'detail' && '配置详情页面布局和字段显示'}
                </div>
              </div>

              {selectedType === 'chart' && (
                <div className="transition-all duration-300 ease-in-out">
                  <ChartConfigStep
                    selectedDataSource={selectedDataSource}
                    relatedDataSources={relatedDataSources}
                    value={chartConfig || undefined}
                    onChange={setChartConfig}
                  />
                </div>
              )}

              {selectedType === 'card' && (
                <div className="transition-all duration-300 ease-in-out">
                  <CardConfigStep
                    selectedDataSource={selectedDataSource}
                    relatedDataSources={relatedDataSources}
                    value={cardConfig || undefined}
                    onChange={setCardConfig}
                  />
                </div>
              )}

              {(selectedType === 'table' || selectedType === 'detail') && (
                <div className="text-center text-gray-500 py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
                  <div className="space-y-3">
                    <div className="text-4xl">🚧</div>
                    <div className="text-lg font-medium">
                      {selectedType === 'table' ? '表格配置' : '详情配置'}
                    </div>
                    <div className="text-sm max-w-md mx-auto">
                      {selectedType === 'table'
                        ? '表格组件配置界面正在开发中，敬请期待！'
                        : '详情组件配置界面正在开发中，敬请期待！'
                      }
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </Form>
      </Modal>


    </>
  );
};

export default CreateComponentModal;
