import { useState } from 'react';
import { Input, Button, Empty, Tag, Flex, List, Avatar } from 'antd';
import { SearchOutlined, PlusOutlined, DashboardOutlined, SettingOutlined, EyeOutlined } from '@ant-design/icons';
import type { PanelItem, PanelType, PanelStatus } from '../types/component';

interface PanelTabProps {
  onPanelSelect?: (panel: PanelItem) => void;
  onCreatePanel?: () => void;
}

// Mock面板数据
const mockPanels: PanelItem[] = [
  {
    id: 'panel-001',
    name: '销售数据仪表板',
    description: '展示销售业绩、趋势分析和关键指标的综合仪表板',
    type: 'dashboard',
    status: 'active',
    createdAt: '2024-01-15',
    updatedAt: '2024-01-20',
    componentCount: 8
  },
  {
    id: 'panel-002',
    name: '用户行为分析报告',
    description: '用户访问路径、停留时间和转化率分析报告',
    type: 'report',
    status: 'active',
    createdAt: '2024-01-10',
    updatedAt: '2024-01-18',
    componentCount: 5
  },
  {
    id: 'panel-003',
    name: '系统监控面板',
    description: '服务器性能、API响应时间和错误率监控',
    type: 'monitor',
    status: 'draft',
    createdAt: '2024-01-12',
    updatedAt: '2024-01-16',
    componentCount: 3
  },
  {
    id: 'panel-004',
    name: '财务数据概览',
    description: '收入、支出、利润等财务关键指标展示',
    type: 'dashboard',
    status: 'inactive',
    createdAt: '2024-01-08',
    updatedAt: '2024-01-14',
    componentCount: 6
  }
];

// 面板类型配置
const panelTypeConfig = {
  dashboard: { label: '仪表板', color: 'blue', icon: <DashboardOutlined /> },
  report: { label: '报告', color: 'green', icon: <EyeOutlined /> },
  monitor: { label: '监控', color: 'orange', icon: <SettingOutlined /> }
};

// 状态配置
const statusConfig = {
  active: { label: '已发布', color: 'green' },
  inactive: { label: '已停用', color: 'red' },
  draft: { label: '草稿', color: 'default' }
};

const PanelTab: React.FC<PanelTabProps> = ({
  onPanelSelect,
  onCreatePanel
}) => {
  const [searchText, setSearchText] = useState<string>('');

  // 搜索过滤
  const filteredPanels = mockPanels.filter(panel =>
    panel.name.toLowerCase().includes(searchText.toLowerCase()) ||
    panel.description.toLowerCase().includes(searchText.toLowerCase())
  );

  const handlePanelClick = (panel: PanelItem) => {
    onPanelSelect?.(panel);
  };

  const handleCreateClick = () => {
    onCreatePanel?.();
  };

  return (
    <div className="h-full flex flex-col">
      {/* 搜索和创建区域 */}
      <div className="p-4 border-b bg-gray-50">
        <Flex gap={12} align="center">
          <Input
            placeholder="搜索面板..."
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            allowClear
            className="flex-1"
          />
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreateClick}
          >
            新建面板
          </Button>
        </Flex>
      </div>

      {/* 面板列表区域 */}
      <div className="flex-1 p-4 overflow-y-auto">
        {filteredPanels.length > 0 ? (
          <List
            dataSource={filteredPanels}
            renderItem={(panel) => (
              <List.Item
                key={panel.id}
                className="cursor-pointer hover:bg-gray-50 rounded-lg p-3 border mb-2"
                onClick={() => handlePanelClick(panel)}
              >
                <List.Item.Meta
                  avatar={
                    <Avatar
                      icon={panelTypeConfig[panel.type].icon}
                      style={{ backgroundColor: `var(--ant-color-${panelTypeConfig[panel.type].color})` }}
                    />
                  }
                  title={
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{panel.name}</span>
                      <Tag color={panelTypeConfig[panel.type].color}>
                        {panelTypeConfig[panel.type].label}
                      </Tag>
                      <Tag color={statusConfig[panel.status].color}>
                        {statusConfig[panel.status].label}
                      </Tag>
                    </div>
                  }
                  description={
                    <div className="space-y-1">
                      <div className="text-gray-600">{panel.description}</div>
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <span>组件数量: {panel.componentCount}</span>
                        <span>创建时间: {panel.createdAt}</span>
                        <span>更新时间: {panel.updatedAt}</span>
                      </div>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        ) : (
          <div className="flex items-center justify-center h-64">
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                searchText ? (
                  <span>未找到匹配的面板</span>
                ) : (
                  <span>暂无面板数据</span>
                )
              }
            >
              {!searchText && (
                <Button type="primary" icon={<PlusOutlined />} onClick={handleCreateClick}>
                  创建第一个面板
                </Button>
              )}
            </Empty>
          )}
        )}
          </div>

      {/* 底部统计信息 */}
        <div className="p-4 border-t bg-gray-50">
          <div className="text-sm text-gray-600">
            共 {filteredPanels.length} 个面板
            {searchText && ` (搜索结果)`}
          </div>
        </div>
      </div>
      );
};

      export default PanelTab;
