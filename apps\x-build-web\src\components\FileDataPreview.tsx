import { Table} from "antd";
import { useMemo } from "react";
import { TableInfo } from "../types";
import { useTableScroll } from "../hooks/useTableScroll";

// 数据预览组件
interface FileDataPreviewProps {
    table: TableInfo;
}

const FileDataPreview = ({ table }: FileDataPreviewProps) => {
    // 缓存表格列定义
    const columns = useMemo(() => {
        return table.fields.map(field => ({
            title: field.name,
            dataIndex: field.originalName,
            key: field.originalName,
            width: 100,
            ellipsis: true,
            render: (text: any) => (
                <span className="text-xs">{String(text)}</span>
            )
        }));
    }, [table.fields]);
    const { tableRef, scroll } = useTableScroll();
    return (
        <div className="border-l min-w-0">
            <h3 className="mb-2">数据预览</h3>
            <Table
                ref={tableRef}
                dataSource={table.data}
                columns={columns}
                pagination={false}
                scroll={{...scroll,x:'100%'}}
            />
        </div>
    );
};

export default FileDataPreview;
