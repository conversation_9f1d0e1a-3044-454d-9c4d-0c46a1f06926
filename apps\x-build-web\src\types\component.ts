// 组件类型枚举
export type ComponentType = 'chart' | 'table' | 'card' | 'detail';

// 组件参数接口
export interface ComponentParameter {
  id: string;
  name: string;
  type: 'string' | 'number' | 'boolean' | 'date' | 'array';
  required: boolean;
  source: 'dataSource' | 'relatedDataSource';
  sourceField?: string; // 来源字段名
  description?: string;
  defaultValue?: any;
}

// 组件项接口
export interface ComponentItem {
  id: string;
  name: string;
  type: ComponentType;
  dataSource: string; // 主数据源ID
  dataSourceName?: string; // 主数据源名称
  relatedDataSources: string[]; // 关联数据源ID列表
  relatedDataSourceNames?: string[]; // 关联数据源名称列表
  parameters: ComponentParameter[]; // 组件入参
  description?: string;
  createdAt: string; // 创建时间
  updatedAt: string; // 更新时间
  config?: Record<string, any>; // 组件配置信息
}

// 数据源信息接口
export interface DataSourceInfo {
  id: string;
  name: string;
  description?: string;
  fields: DataSourceField[]; // 数据源字段列表
  type?: 'table' | 'view' | 'api';
}

// 数据源字段接口
export interface DataSourceField {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'date' | 'array';
  description?: string;
  nullable?: boolean;
}

// 新建组件表单接口
export interface CreateComponentForm {
  name: string;
  dataSource: string; // 选择的数据源ID
  relatedDataSources?: string[]; // 关联数据源ID列表
  type: ComponentType;
  parameters: ComponentParameter[];
  description?: string;
}

// 组件分类信息
export interface ComponentCategory {
  type: ComponentType;
  label: string;
  icon?: string;
  description?: string;
}

// 参数选择弹窗的表单数据
export interface ParameterSelectionForm {
  selectedParameters: string[]; // 选中的参数ID列表
  customParameters?: ComponentParameter[]; // 自定义参数
}

// 组件预览信息
export interface ComponentPreviewInfo {
  component: ComponentItem;
  dataSourceInfo: DataSourceInfo;
  relatedDataSourcesInfo: DataSourceInfo[];
  previewData?: any[]; // 预览数据
}

// 组件搜索过滤条件
export interface ComponentSearchFilter {
  keyword?: string;
  type?: ComponentType;
  dataSource?: string;
}

// 组件操作类型
export type ComponentAction = 'create' | 'edit' | 'delete' | 'copy' | 'preview';

// 组件状态
export type ComponentStatus = 'draft' | 'published' | 'archived';

// 扩展的组件项（包含状态信息）
export interface ExtendedComponentItem extends ComponentItem {
  status: ComponentStatus;
  version?: string;
  author?: string;
  lastModifiedBy?: string;
}
