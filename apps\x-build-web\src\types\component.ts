// 组件类型枚举
export type ComponentType = 'chart' | 'table' | 'card' | 'detail';

// 图表类型枚举
export type ChartType =
  | 'bar'        // 柱状图
  | 'histogram'  // 直方图
  | 'line'       // 趋势图
  | 'area'       // 面积图
  | 'pie'        // 饼图
  | 'doughnut'   // 环形图
  | 'tree'       // 树状图
  | 'scatter'    // 散点图
  | 'radar'      // 雷达图
  | 'funnel'     // 漏斗图
  | 'combo'      // 组合图
  | 'graph'      // 关系图
  | 'wordcloud'; // 词云

// 卡片类型枚举
export type CardType =
  | 'single'      // 单卡片
  | 'dual'        // 双指标卡片
  | 'multiple';   // 多指标卡片

// 面板类型枚举
export type PanelType = 'dashboard' | 'report' | 'monitor';

// 面板状态枚举
export type PanelStatus = 'active' | 'inactive' | 'draft';

// 组件参数接口
export interface ComponentParameter {
  id: string;
  name: string;
  type: 'string' | 'number' | 'boolean' | 'date' | 'array';
  required: boolean;
  source: 'dataSource' | 'relatedDataSource';
  sourceField?: string; // 来源字段名
  description?: string;
  defaultValue?: any;
}

// 组件项接口
export interface ComponentItem {
  id: string;
  name: string;
  type: ComponentType;
  dataSource: string; // 主数据源ID
  dataSourceName?: string; // 主数据源名称
  relatedDataSources: string[]; // 关联数据源ID列表
  relatedDataSourceNames?: string[]; // 关联数据源名称列表
  parameters: ComponentParameter[]; // 组件入参
  description?: string;
  createdAt: string; // 创建时间
  updatedAt: string; // 更新时间
  config?: Record<string, any>; // 组件配置信息
}

// 数据源信息接口
export interface DataSourceInfo {
  id: string;
  name: string;
  description?: string;
  fields: DataSourceField[]; // 数据源字段列表
  type?: 'table' | 'view' | 'api';
}

// 数据源字段接口
export interface DataSourceField {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'date' | 'array';
  description?: string;
  nullable?: boolean;
}

// 图表类型配置接口
export interface ChartTypeConfig {
  type: ChartType;
  label: string;
  icon: string; // Ant Design 图标名称
  description?: string;
  requiredDimensions: number; // 所需维度数量
  requiredMetrics: number; // 所需指标数量
  maxDimensions?: number; // 最大维度数量
  maxMetrics?: number; // 最大指标数量
}

// 维度指标字段接口
export interface DimensionMetricField {
  id: string;
  name: string;
  type: 'dimension' | 'metric';
  fieldName: string; // 对应的数据源字段名
  dataSourceId: string; // 来源数据源ID
  aggregation?: 'sum' | 'count' | 'avg' | 'max' | 'min'; // 聚合函数（仅指标）
  sort?: 'asc' | 'desc' | 'none'; // 排序方式
}

// 图表配置接口
export interface ChartConfig {
  chartType: ChartType;
  dimensions: DimensionMetricField[]; // 维度字段
  metrics: DimensionMetricField[]; // 指标字段
  title?: string;
  showLegend?: boolean;
  showDataLabels?: boolean;
  colors?: string[]; // 自定义颜色
}

// 字体配置接口
export interface FontConfig {
  size: number; // 字体大小
  weight: 'normal' | 'bold' | 'bolder' | 'lighter'; // 字体粗细
  color: string; // 字体颜色
  family?: string; // 字体族
}

// 卡片字段配置接口
export interface CardField {
  id: string;
  name: string;
  fieldName: string; // 对应的数据源字段名
  dataSourceId: string; // 来源数据源ID
  aggregation?: 'sum' | 'count' | 'avg' | 'max' | 'min' | 'first' | 'last'; // 聚合函数
  format?: string; // 数值格式化
  prefix?: string; // 前缀
  suffix?: string; // 后缀
}

// 卡片类型配置接口
export interface CardTypeConfig {
  type: CardType;
  label: string;
  icon: string; // Ant Design 图标名称
  description?: string;
  maxFields: number; // 最大字段数量
  minFields: number; // 最小字段数量
  layout: 'horizontal' | 'vertical' | 'grid'; // 布局方式
}

// 卡片配置接口
export interface CardConfig {
  cardType: CardType;
  fields: CardField[]; // 选择的字段
  title?: string;
  showIcon?: boolean; // 是否显示图标
  icon?: string; // 图标名称
  showDescription?: boolean; // 是否显示说明
  description?: string; // 说明文字
  titleFont?: FontConfig; // 标题字体配置
  valueFont?: FontConfig; // 数值字体配置
  descriptionFont?: FontConfig; // 说明字体配置
  backgroundColor?: string; // 背景色
  borderColor?: string; // 边框色
  borderRadius?: number; // 圆角
}

// 新建组件表单接口
export interface CreateComponentForm {
  name: string;
  dataSource: string; // 选择的数据源ID
  relatedDataSources?: string[]; // 关联数据源ID列表
  type: ComponentType;
  parameters: ComponentParameter[];
  description?: string;
}

// 组件分类信息
export interface ComponentCategory {
  type: ComponentType;
  label: string;
  icon?: string;
  description?: string;
}



// 参数选择弹窗的表单数据
export interface ParameterSelectionForm {
  selectedParameters: string[]; // 选中的参数ID列表
  customParameters?: ComponentParameter[]; // 自定义参数
}

// 组件预览信息
export interface ComponentPreviewInfo {
  component: ComponentItem;
  dataSourceInfo: DataSourceInfo;
  relatedDataSourcesInfo: DataSourceInfo[];
  previewData?: any[]; // 预览数据
}

// 组件搜索过滤条件
export interface ComponentSearchFilter {
  keyword?: string;
  type?: ComponentType;
  dataSource?: string;
}

// 组件操作类型
export type ComponentAction = 'create' | 'edit' | 'delete' | 'copy' | 'preview';

// 组件状态
export type ComponentStatus = 'draft' | 'published' | 'archived';

// 扩展的组件项（包含状态信息）
export interface ExtendedComponentItem extends ComponentItem {
  status: ComponentStatus;
  version?: string;
  author?: string;
  lastModifiedBy?: string;
}

// 面板项接口
export interface PanelItem {
  id: string;
  name: string;
  description: string;
  type: PanelType;
  status: PanelStatus;
  createdAt: string;
  updatedAt: string;
  componentCount: number;
}

// 图表类型配置常量
export const CHART_TYPE_CONFIGS: Record<ChartType, ChartTypeConfig> = {
  bar: {
    type: 'bar',
    label: '柱状图',
    icon: 'BarChartOutlined',
    description: '用于比较不同类别的数值',
    requiredDimensions: 1,
    requiredMetrics: 1,
    maxDimensions: 2,
    maxMetrics: 3
  },
  histogram: {
    type: 'histogram',
    label: '直方图',
    icon: 'AreaChartOutlined',
    description: '显示数据分布情况',
    requiredDimensions: 1,
    requiredMetrics: 1,
    maxDimensions: 1,
    maxMetrics: 1
  },
  line: {
    type: 'line',
    label: '趋势图',
    icon: 'LineChartOutlined',
    description: '显示数据随时间的变化趋势',
    requiredDimensions: 1,
    requiredMetrics: 1,
    maxDimensions: 2,
    maxMetrics: 3
  },
  area: {
    type: 'area',
    label: '面积图',
    icon: 'AreaChartOutlined',
    description: '强调数量随时间的变化',
    requiredDimensions: 1,
    requiredMetrics: 1,
    maxDimensions: 2,
    maxMetrics: 2
  },
  pie: {
    type: 'pie',
    label: '饼图',
    icon: 'PieChartOutlined',
    description: '显示各部分占整体的比例',
    requiredDimensions: 1,
    requiredMetrics: 1,
    maxDimensions: 1,
    maxMetrics: 1
  },
  doughnut: {
    type: 'doughnut',
    label: '环形图',
    icon: 'PieChartOutlined',
    description: '环形饼图，中心可显示总计',
    requiredDimensions: 1,
    requiredMetrics: 1,
    maxDimensions: 1,
    maxMetrics: 1
  },
  tree: {
    type: 'tree',
    label: '树状图',
    icon: 'PartitionOutlined',
    description: '显示层次结构数据',
    requiredDimensions: 2,
    requiredMetrics: 1,
    maxDimensions: 3,
    maxMetrics: 1
  },
  scatter: {
    type: 'scatter',
    label: '散点图',
    icon: 'DotChartOutlined',
    description: '显示两个变量之间的关系',
    requiredDimensions: 0,
    requiredMetrics: 2,
    maxDimensions: 1,
    maxMetrics: 3
  },
  radar: {
    type: 'radar',
    label: '雷达图',
    icon: 'RadarChartOutlined',
    description: '多维度数据对比',
    requiredDimensions: 1,
    requiredMetrics: 3,
    maxDimensions: 1,
    maxMetrics: 8
  },
  funnel: {
    type: 'funnel',
    label: '漏斗图',
    icon: 'FunnelPlotOutlined',
    description: '显示流程各阶段的转化率',
    requiredDimensions: 1,
    requiredMetrics: 1,
    maxDimensions: 1,
    maxMetrics: 1
  },
  combo: {
    type: 'combo',
    label: '组合图',
    icon: 'StockOutlined',
    description: '组合多种图表类型',
    requiredDimensions: 1,
    requiredMetrics: 2,
    maxDimensions: 2,
    maxMetrics: 4
  },
  graph: {
    type: 'graph',
    label: '关系图',
    icon: 'ShareAltOutlined',
    description: '显示节点间的关系网络',
    requiredDimensions: 2,
    requiredMetrics: 0,
    maxDimensions: 3,
    maxMetrics: 2
  },
  wordcloud: {
    type: 'wordcloud',
    label: '词云',
    icon: 'CloudOutlined',
    description: '文本数据可视化',
    requiredDimensions: 1,
    requiredMetrics: 1,
    maxDimensions: 1,
    maxMetrics: 1
  }
};

// 图表类型选项列表（用于UI选择）
export const CHART_TYPE_OPTIONS = Object.values(CHART_TYPE_CONFIGS);

// 卡片类型配置常量
export const CARD_TYPE_CONFIGS: Record<CardType, CardTypeConfig> = {
  single: {
    type: 'single',
    label: '单卡片',
    icon: 'IdcardOutlined',
    description: '显示单个指标数值',
    maxFields: 1,
    minFields: 1,
    layout: 'vertical'
  },
  dual: {
    type: 'dual',
    label: '双指标卡片',
    icon: 'CreditCardOutlined',
    description: '并排显示两个指标',
    maxFields: 2,
    minFields: 2,
    layout: 'horizontal'
  },
  multiple: {
    type: 'multiple',
    label: '多指标卡片',
    icon: 'TableOutlined',
    description: '网格布局显示多个指标',
    maxFields: 8,
    minFields: 3,
    layout: 'grid'
  }
};

// 卡片类型选项列表（用于UI选择）
export const CARD_TYPE_OPTIONS = Object.values(CARD_TYPE_CONFIGS);

// 默认字体配置
export const DEFAULT_FONT_CONFIG: FontConfig = {
  size: 14,
  weight: 'normal',
  color: '#333333',
  family: 'Arial, sans-serif'
};

// 卡片字体配置预设
export const CARD_FONT_PRESETS = {
  title: {
    size: 16,
    weight: 'bold' as const,
    color: '#1f1f1f'
  },
  value: {
    size: 24,
    weight: 'bold' as const,
    color: '#1890ff'
  },
  description: {
    size: 12,
    weight: 'normal' as const,
    color: '#666666'
  }
};
