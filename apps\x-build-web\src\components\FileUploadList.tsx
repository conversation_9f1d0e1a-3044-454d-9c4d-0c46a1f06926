import { CloseOutlined, EditOutlined, EyeOutlined, DeleteOutlined, UploadOutlined } from "@ant-design/icons";
import { Card, Progress, Button, Checkbox, Tooltip } from "antd";
import { memo, useCallback } from "react";
import { TableInfo } from "../types";

// 文件列表组件
interface FileUploadListProps {
    tables: TableInfo[];
    selectedTableId: string;
    uploadingFiles: Set<string>;
    fileProgress: Map<string, number>;
    onTableSelect: (tableId: string) => void;
    onTableToggle: (tableId: string, selected: boolean) => void;
    onDeleteTable: (tableId: string) => void;
    onCancelParse: (fileId: string) => void;
    onEditAlias: (table: TableInfo) => void;
    onSetPrimaryKey: (table: TableInfo) => void;
}

const FileUploadList = ({
    tables,
    selectedTableId,
    uploadingFiles,
    fileProgress,
    onTableSelect,
    onTableToggle,
    onDeleteTable,
    onCancelParse,
    onEditAlias,
    onSetPrimaryKey
}: FileUploadListProps) => {
    // 缓存表格选择回调
    const handleTableClick = useCallback((tableId: string) => {
        onTableSelect(tableId);
    }, [onTableSelect]);

    // 缓存表格切换回调
    const handleTableToggle = useCallback((tableId: string, checked: boolean, e: any) => {
        e.stopPropagation();
        onTableToggle(tableId, checked);
    }, [onTableToggle]);

    return (
        <div className="flex-1 overflow-y-auto p-2 space-y-2">
            {/* 正在上传的文件 */}
            {Array.from(uploadingFiles).map(fileId => (
                <Card
                    key={fileId}
                    size="small"
                    className="border-orange-200 bg-orange-50"
                >
                    <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                            <div className="text-sm font-medium text-orange-800 truncate">
                                {fileId.split('_')[0]}
                            </div>
                            <div className="text-xs text-orange-600">
                                解析中... {Math.round((fileProgress.get(fileId) || 0))}%
                            </div>
                            <Progress
                                percent={Math.round((fileProgress.get(fileId) || 0))}
                                size="small"
                                status="active"
                                className="mt-1"
                            />
                        </div>
                        <Button
                            type="text"
                            size="small"
                            icon={<CloseOutlined />}
                            onClick={() => onCancelParse(fileId)}
                            className="text-orange-600 hover:text-orange-800"
                        />
                    </div>
                </Card>
            ))}

            {/* 已解析的文件列表 */}
            {tables.map(table => (
                <Card
                    key={table.id}
                    size="small"
                    className={`cursor-pointer transition-colors ${selectedTableId === table.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'hover:border-gray-300'
                        }`}
                    onClick={() => handleTableClick(table.id)}
                >
                    <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2">
                                <Checkbox
                                    checked={table.selected}
                                    onChange={(e) => handleTableToggle(table.id, e.target.checked, e)}
                                />
                                <div className="flex-1 min-w-0">
                                    <div className="text-sm font-medium truncate">{table.alias}</div>
                                    <div className="text-xs text-gray-500 truncate">{table.fileName}</div>
                                    <div className="text-xs text-gray-400">
                                        {table.fields.length} 字段 · {table.data.length} 行
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="flex space-x-1">
                            <Tooltip title="编辑别名">
                                <Button
                                    type="text"
                                    size="small"
                                    icon={<EditOutlined />}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        onEditAlias(table);
                                    }}
                                />
                            </Tooltip>
                            <Tooltip title="设置主键">
                                <Button
                                    type="text"
                                    size="small"
                                    icon={<EyeOutlined />}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        onSetPrimaryKey(table);
                                    }}
                                />
                            </Tooltip>
                            <Tooltip title="删除">
                                <Button
                                    type="text"
                                    size="small"
                                    icon={<DeleteOutlined />}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        onDeleteTable(table.id);
                                    }}
                                    className="text-red-500 hover:text-red-700"
                                />
                            </Tooltip>
                        </div>
                    </div>
                </Card>
            ))}

            {/* 空状态 */}
            {tables.length === 0 && uploadingFiles.size === 0 && (
                <div className="text-center py-8 text-gray-400">
                    <UploadOutlined className="text-3xl mb-2" />
                    <div className="text-sm">暂无上传文件</div>
                    <div className="text-xs">请点击上方按钮上传CSV文件</div>
                </div>
            )}
        </div>
    );
};

export default memo(FileUploadList);
