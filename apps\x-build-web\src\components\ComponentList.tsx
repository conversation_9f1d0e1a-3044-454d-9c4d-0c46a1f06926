import { useState, useMemo } from 'react';
import { Input, Button, Collapse, Empty, Tag, Flex } from 'antd';
import { SearchOutlined, PlusOutlined, AppstoreOutlined, TableOutlined, IdcardOutlined, ProfileOutlined } from '@ant-design/icons';
import type { CollapseProps } from 'antd';
import type { ComponentItem, ComponentType } from '../types/component';
import { mockComponents, mockComponentCategories } from '../mock/componentData';

interface ComponentListProps {
  onComponentSelect?: (component: ComponentItem) => void;
  onCreateComponent?: () => void;
}

// 组件类型图标映射
const componentTypeIcons: Record<ComponentType, React.ReactNode> = {
  chart: <AppstoreOutlined className="text-blue-500" />,
  table: <TableOutlined className="text-green-500" />,
  card: <IdcardOutlined className="text-orange-500" />,
  detail: <ProfileOutlined className="text-purple-500" />
};

// 组件类型颜色映射
const componentTypeColors: Record<ComponentType, string> = {
  chart: 'blue',
  table: 'green',
  card: 'orange',
  detail: 'purple'
};

const ComponentList: React.FC<ComponentListProps> = ({
  onComponentSelect,
  onCreateComponent
}) => {
  const [searchValue, setSearchValue] = useState<string>('');

  // 搜索过滤组件
  const filteredComponents = useMemo(() => {
    if (!searchValue.trim()) {
      return mockComponents;
    }
    
    const keyword = searchValue.toLowerCase();
    return mockComponents.filter(component =>
      component.name.toLowerCase().includes(keyword) ||
      component.description?.toLowerCase().includes(keyword) ||
      component.dataSourceName?.toLowerCase().includes(keyword)
    );
  }, [searchValue]);

  // 按类型分组组件
  const groupedComponents = useMemo(() => {
    const groups: Record<ComponentType, ComponentItem[]> = {
      chart: [],
      table: [],
      card: [],
      detail: []
    };

    filteredComponents.forEach(component => {
      groups[component.type].push(component);
    });

    return groups;
  }, [filteredComponents]);

  // 渲染组件项
  const renderComponentItem = (component: ComponentItem) => (
    <div
      key={component.id}
      className="p-3 cursor-pointer hover:bg-blue-50 rounded border border-gray-200 hover:border-blue-300 transition-all mb-2"
      onClick={() => onComponentSelect?.(component)}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            {componentTypeIcons[component.type]}
            <span className="font-medium text-gray-900">{component.name}</span>
            <Tag color={componentTypeColors[component.type]} >
              {mockComponentCategories.find(cat => cat.type === component.type)?.label}
            </Tag>
          </div>
          
          {component.description && (
            <p className="text-sm text-gray-600 mb-2 line-clamp-2">
              {component.description}
            </p>
          )}
          
          <div className="flex flex-wrap gap-1">
            <Tag  color="default">
              {component.dataSourceName}
            </Tag>
            {component.relatedDataSourceNames?.map((name, index) => (
              <Tag key={index}  color="default" className="opacity-75">
                {name}
              </Tag>
            ))}
          </div>
        </div>
      </div>
      
      <div className="flex justify-between items-center mt-2 text-xs text-gray-500">
        <span>参数: {component.parameters.length}个</span>
        <span>{new Date(component.updatedAt).toLocaleDateString()}</span>
      </div>
    </div>
  );

  // 构建Collapse项
  const collapseItems: CollapseProps['items'] = mockComponentCategories.map(category => ({
    key: category.type,
    label: (
      <div className="flex items-center gap-2">
        {componentTypeIcons[category.type]}
        <span>{category.label}</span>
        <Tag  color={componentTypeColors[category.type]}>
          {groupedComponents[category.type].length}
        </Tag>
      </div>
    ),
    children: groupedComponents[category.type].length > 0 ? (
      <div className="space-y-0">
        {groupedComponents[category.type].map(renderComponentItem)}
      </div>
    ) : (
      <Empty 
        description={searchValue ? '未找到匹配的组件' : '暂无此类型组件'} 
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        className="py-4"
      />
    )
  }));

  return (
    <Flex vertical gap="small" className="flex-1 h-full">
      {/* 搜索框 */}
      <Input
        placeholder="搜索组件名称、描述或数据源"
        prefix={<SearchOutlined />}
        value={searchValue}
        onChange={(e) => setSearchValue(e.target.value)}
        allowClear
        size="small"
      />

      {/* 新建组件按钮 */}
      <Button
        type="primary"
        icon={<PlusOutlined />}
        onClick={onCreateComponent}
        className="w-full"
        size="small"
      >
        新建组件
      </Button>

      {/* 组件列表 */}
      <div className="flex-1 overflow-y-auto h-[calc(100vh-210px)]">
        {filteredComponents.length > 0 ? (
          <Collapse
            size="small"
            ghost
            className="flex-1"
            items={collapseItems}
            defaultActiveKey={mockComponentCategories.map(cat => cat.type)}
          />
        ) : (
          <Empty 
            description={searchValue ? '未找到匹配的组件' : '暂无组件，点击"新建组件"开始创建'}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            className="mt-10"
          />
        )}
      </div>
    </Flex>
  );
};

export default ComponentList;
