import { Card, Button, Tag, Descriptions, Empty, Divider } from 'antd';
import { EditOutlined, AppstoreOutlined, TableOutlined, IdcardOutlined, ProfileOutlined, DatabaseOutlined, CalendarOutlined } from '@ant-design/icons';
import type { ComponentItem, ComponentType } from '../types/component';

interface ComponentPreviewProps {
  component?: ComponentItem;
  onEdit?: (component: ComponentItem) => void;
}

// 组件类型图标映射
const componentTypeIcons: Record<ComponentType, React.ReactNode> = {
  chart: <AppstoreOutlined className="text-blue-500" />,
  table: <TableOutlined className="text-green-500" />,
  card: <IdcardOutlined className="text-orange-500" />,
  detail: <ProfileOutlined className="text-purple-500" />
};

// 组件类型颜色映射
const componentTypeColors: Record<ComponentType, string> = {
  chart: 'blue',
  table: 'green',
  card: 'orange',
  detail: 'purple'
};

// 组件类型名称映射
const componentTypeNames: Record<ComponentType, string> = {
  chart: '图表组件',
  table: '表格组件',
  card: '卡片组件',
  detail: '详情组件'
};

// 根据组件类型渲染预览内容
const renderPreviewContent = (component: ComponentItem) => {
  const commonStyle = "h-48 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200 flex items-center justify-center";
  
  switch (component.type) {
    case 'chart':
      return (
        <div className={commonStyle}>
          <div className="text-center text-gray-400">
            <AppstoreOutlined className="text-4xl mb-2" />
            <div className="text-lg mb-1">图表预览</div>
            <div className="text-sm">
              {component.config?.chartType ? `${component.config.chartType}图表` : '数据可视化图表'}
            </div>
          </div>
        </div>
      );
    
    case 'table':
      return (
        <div className={commonStyle}>
          <div className="text-center text-gray-400">
            <TableOutlined className="text-4xl mb-2" />
            <div className="text-lg mb-1">表格预览</div>
            <div className="text-sm">
              数据表格 - {component.config?.pageSize ? `每页${component.config.pageSize}条` : '分页展示'}
            </div>
          </div>
        </div>
      );
    
    case 'card':
      return (
        <div className={commonStyle}>
          <div className="text-center text-gray-400">
            <IdcardOutlined className="text-4xl mb-2" />
            <div className="text-lg mb-1">卡片预览</div>
            <div className="text-sm">
              {component.config?.layout ? `${component.config.layout}布局` : '指标卡片'}
            </div>
          </div>
        </div>
      );
    
    case 'detail':
      return (
        <div className={commonStyle}>
          <div className="text-center text-gray-400">
            <ProfileOutlined className="text-4xl mb-2" />
            <div className="text-lg mb-1">详情预览</div>
            <div className="text-sm">
              {component.config?.layout ? `${component.config.layout}布局` : '详情页面'}
            </div>
          </div>
        </div>
      );
    
    default:
      return (
        <div className={commonStyle}>
          <div className="text-center text-gray-400">
            <AppstoreOutlined className="text-4xl mb-2" />
            <div className="text-lg mb-1">组件预览</div>
            <div className="text-sm">暂无预览内容</div>
          </div>
        </div>
      );
  }
};

const ComponentPreview: React.FC<ComponentPreviewProps> = ({
  component,
  onEdit
}) => {
  // 未选择组件时的占位内容
  if (!component) {
    return (
      <div className="h-full flex items-center justify-center">
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={
            <div className="text-gray-400">
              <div className="text-lg mb-2">请选择一个组件</div>
              <div className="text-sm">从左侧组件列表中选择一个组件进行预览</div>
            </div>
          }
        />
      </div>
    );
  }

  return (
    <div className="p-4 h-full overflow-auto">
      <Card
        title={
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {componentTypeIcons[component.type]}
              <span className="text-lg font-medium">{component.name}</span>
              <Tag color={componentTypeColors[component.type]}>
                {componentTypeNames[component.type]}
              </Tag>
            </div>
            <Button
              type="primary"
              icon={<EditOutlined />}
              onClick={() => onEdit?.(component)}
              size="small"
            >
              编辑组件
            </Button>
          </div>
        }
        className="h-full"
        bodyStyle={{ height: 'calc(100% - 60px)', overflow: 'auto' }}
      >
        {/* 组件基本信息 */}
        <Descriptions
          column={2}
          size="small"
          className="mb-4"
          items={[
            {
              key: 'dataSource',
              label: (
                <span>
                  <DatabaseOutlined className="mr-1" />
                  主数据源
                </span>
              ),
              children: component.dataSourceName || component.dataSource
            },
            {
              key: 'parameters',
              label: '参数数量',
              children: `${component.parameters.length}个`
            },
            {
              key: 'createdAt',
              label: (
                <span>
                  <CalendarOutlined className="mr-1" />
                  创建时间
                </span>
              ),
              children: new Date(component.createdAt).toLocaleString()
            },
            {
              key: 'updatedAt',
              label: '更新时间',
              children: new Date(component.updatedAt).toLocaleString()
            }
          ]}
        />

        {/* 组件描述 */}
        {component.description && (
          <>
            <Divider orientation="left" orientationMargin="0">
              组件描述
            </Divider>
            <p className="text-gray-600 mb-4">{component.description}</p>
          </>
        )}

        {/* 关联数据源 */}
        {component.relatedDataSourceNames && component.relatedDataSourceNames.length > 0 && (
          <>
            <Divider orientation="left" orientationMargin="0">
              关联数据源
            </Divider>
            <div className="mb-4">
              {component.relatedDataSourceNames.map((name, index) => (
                <Tag key={index} color="default" className="mb-1">
                  {name}
                </Tag>
              ))}
            </div>
          </>
        )}

        {/* 组件参数 */}
        {component.parameters.length > 0 && (
          <>
            <Divider orientation="left" orientationMargin="0">
              组件参数
            </Divider>
            <div className="mb-4">
              {component.parameters.map((param) => (
                <Tag
                  key={param.id}
                  color={param.required ? 'red' : 'default'}
                  className="mb-1"
                >
                  {param.name} ({param.type})
                  {param.required && ' *'}
                </Tag>
              ))}
            </div>
          </>
        )}

        {/* 组件预览 */}
        <Divider orientation="left" orientationMargin="0">
          组件预览
        </Divider>
        {renderPreviewContent(component)}
      </Card>
    </div>
  );
};

export default ComponentPreview;
