import { useState, useEffect } from 'react';
import {
  Radio,
  Card,
  Checkbox,
  Form,
  Input,
  Select,
  Switch,
  ColorPicker,
  InputNumber,
  Space,
  Divider,
  Tag,
  Button,
  Tooltip,
  Empty
} from 'antd';
import {
  IdcardOutlined,
  CreditCardOutlined,
  TableOutlined,
  StarOutlined,
  HeartOutlined,
  TrophyOutlined,
  CrownOutlined,
  FireOutlined,
  <PERSON>boltOutlined,
  BulbOutlined,
  RocketOutlined,
  GiftOutlined,
  DeleteOutlined,
  FieldStringOutlined,
  FieldNumberOutlined,
  FieldTimeOutlined,
  FieldBinaryOutlined,
  EyeOutlined
} from '@ant-design/icons';
import type {
  CardType,
  CardConfig,
  CardField,
  DataSourceField,
  FontConfig
} from '../types/component';
import { CARD_TYPE_CONFIGS, CARD_TYPE_OPTIONS, CARD_FONT_PRESETS } from '../types/component';
import { mockDataSources } from '../mock/componentData';
import CardPreview from './CardPreview';

interface CardConfigStepProps {
  selectedDataSource: string;
  relatedDataSources: string[];
  value?: CardConfig;
  onChange?: (config: CardConfig) => void;
}

// 卡片类型图标映射
const cardTypeIcons: Record<CardType, React.ReactNode> = {
  single: <IdcardOutlined />,
  dual: <CreditCardOutlined />,
  multiple: <TableOutlined />
};

// 字段类型图标映射
const fieldTypeIcons: Record<string, React.ReactNode> = {
  string: <FieldStringOutlined className="text-blue-500" />,
  number: <FieldNumberOutlined className="text-green-500" />,
  date: <FieldTimeOutlined className="text-orange-500" />,
  boolean: <FieldBinaryOutlined className="text-purple-500" />,
  array: <FieldStringOutlined className="text-gray-500" />
};

// 可选图标列表
const availableIcons = [
  { key: 'star', icon: <StarOutlined />, label: '星星' },
  { key: 'heart', icon: <HeartOutlined />, label: '心形' },
  { key: 'trophy', icon: <TrophyOutlined />, label: '奖杯' },
  { key: 'crown', icon: <CrownOutlined />, label: '皇冠' },
  { key: 'fire', icon: <FireOutlined />, label: '火焰' },
  { key: 'thunder', icon: <ThunderboltOutlined />, label: '闪电' },
  { key: 'bulb', icon: <BulbOutlined />, label: '灯泡' },
  { key: 'rocket', icon: <RocketOutlined />, label: '火箭' },
  { key: 'gift', icon: <GiftOutlined />, label: '礼物' }
];

// 聚合函数选项
const aggregationOptions = [
  { label: '求和', value: 'sum' },
  { label: '计数', value: 'count' },
  { label: '平均值', value: 'avg' },
  { label: '最大值', value: 'max' },
  { label: '最小值', value: 'min' },
  { label: '第一个', value: 'first' },
  { label: '最后一个', value: 'last' }
];

const CardConfigStep: React.FC<CardConfigStepProps> = ({
  selectedDataSource,
  relatedDataSources,
  value,
  onChange
}) => {
  const [form] = Form.useForm();
  const [cardType, setCardType] = useState<CardType>(value?.cardType || 'single');
  const [selectedFields, setSelectedFields] = useState<CardField[]>(value?.fields || []);
  const [showIcon, setShowIcon] = useState<boolean>(value?.showIcon ?? true);
  const [selectedIcon, setSelectedIcon] = useState<string>(value?.icon || 'star');
  const [showDescription, setShowDescription] = useState<boolean>(value?.showDescription ?? true);
  const [showPreview, setShowPreview] = useState<boolean>(true);

  // 获取所有相关数据源的字段
  const allDataSources = [selectedDataSource, ...relatedDataSources];
  const availableFields: { dataSource: any; fields: DataSourceField[] }[] = allDataSources.map(dsId => {
    const ds = mockDataSources.find(d => d.id === dsId);
    return {
      dataSource: ds,
      fields: ds?.fields || []
    };
  }).filter(item => item.dataSource);

  // 获取当前卡片类型配置
  const currentCardConfig = CARD_TYPE_CONFIGS[cardType];

  // 处理卡片类型变化
  const handleCardTypeChange = (type: CardType) => {
    setCardType(type);
    const config = CARD_TYPE_CONFIGS[type];

    // 调整字段数量以符合新类型要求
    if (selectedFields.length > config.maxFields) {
      setSelectedFields(selectedFields.slice(0, config.maxFields));
    } else if (selectedFields.length < config.minFields) {
      // 如果字段不足，保持现有字段，用户需要手动添加
    }
  };

  // 处理字段选择变化
  const handleFieldChange = (dataSourceId: string, fieldName: string, checked: boolean) => {
    if (checked) {
      // 检查是否超过最大字段数
      if (selectedFields.length >= currentCardConfig.maxFields) {
        return;
      }

      const dataSource = mockDataSources.find(ds => ds.id === dataSourceId);
      const field = dataSource?.fields?.find(f => f.name === fieldName);

      if (field) {
        const newField: CardField = {
          id: `${dataSourceId}-${fieldName}-${Date.now()}`,
          name: field.name,
          fieldName: field.name,
          dataSourceId,
          aggregation: field.type === 'number' ? 'sum' : 'count'
        };
        setSelectedFields([...selectedFields, newField]);
      }
    } else {
      setSelectedFields(selectedFields.filter(f =>
        !(f.dataSourceId === dataSourceId && f.fieldName === fieldName)
      ));
    }
  };

  // 移除字段
  const handleRemoveField = (fieldId: string) => {
    setSelectedFields(selectedFields.filter(f => f.id !== fieldId));
  };

  // 更新字段配置
  const handleFieldConfigChange = (fieldId: string, key: keyof CardField, value: any) => {
    setSelectedFields(selectedFields.map(field =>
      field.id === fieldId ? { ...field, [key]: value } : field
    ));
  };

  // 检查字段是否已选择
  const isFieldSelected = (dataSourceId: string, fieldName: string) => {
    return selectedFields.some(f => f.dataSourceId === dataSourceId && f.fieldName === fieldName);
  };

  // 更新配置
  useEffect(() => {
    const formValues = form.getFieldsValue();
    const config: CardConfig = {
      cardType,
      fields: selectedFields,
      title: formValues.title || `${currentCardConfig.label}`,
      showIcon,
      icon: selectedIcon,
      showDescription,
      description: formValues.description,
      titleFont: {
        ...CARD_FONT_PRESETS.title,
        size: formValues.titleFontSize || CARD_FONT_PRESETS.title.size,
        color: formValues.titleFontColor || CARD_FONT_PRESETS.title.color
      },
      valueFont: {
        ...CARD_FONT_PRESETS.value,
        size: formValues.valueFontSize || CARD_FONT_PRESETS.value.size,
        color: formValues.valueFontColor || CARD_FONT_PRESETS.value.color
      },
      descriptionFont: {
        ...CARD_FONT_PRESETS.description,
        size: formValues.descriptionFontSize || CARD_FONT_PRESETS.description.size,
        color: formValues.descriptionFontColor || CARD_FONT_PRESETS.description.color
      },
      backgroundColor: formValues.backgroundColor || '#ffffff',
      borderColor: formValues.borderColor || '#d9d9d9',
      borderRadius: formValues.borderRadius || 6
    };
    onChange?.(config);
  }, [cardType, selectedFields, showIcon, selectedIcon, showDescription, form, currentCardConfig, onChange]);

  return (
    <div className="space-y-6 animate-fadeIn max-h-full">
      {/* 卡片类型选择 */}
      <Card
        title="选择卡片类型"
        size="small"
        className="shadow-sm hover:shadow-md transition-shadow duration-200"
      >
        <Radio.Group
          value={cardType}
          onChange={(e) => handleCardTypeChange(e.target.value)}
          className="w-full"
        >
          <div className="grid grid-cols-3 gap-4">
            {CARD_TYPE_OPTIONS.map(config => (
              <Radio.Button
                key={config.type}
                value={config.type}
                className="flex flex-col items-center p-4 h-auto"
              >
                <div className="text-2xl mb-2">
                  {cardTypeIcons[config.type]}
                </div>
                <div className="text-center">
                  <div className="font-medium">{config.label}</div>
                  <div className="text-xs text-gray-500 mt-1">{config.description}</div>
                </div>
              </Radio.Button>
            ))}
          </div>
        </Radio.Group>

        {/* 卡片类型说明 */}
        <div className="mt-3 p-3 bg-blue-50 rounded">
          <div className="text-sm text-blue-700">
            <strong>{currentCardConfig.label}</strong>: {currentCardConfig.description}
          </div>
          <div className="text-xs text-blue-600 mt-1">
            字段数量: {currentCardConfig.minFields}-{currentCardConfig.maxFields} 个，
            布局方式: {currentCardConfig.layout === 'vertical' ? '垂直' :
              currentCardConfig.layout === 'horizontal' ? '水平' : '网格'}
          </div>
        </div>
      </Card>

      <div className="grid grid-cols-3 gap-6">
        {/* 字段选择 */}
        <Card
          title="选择字段"
          size="small"
          className="shadow-sm hover:shadow-md transition-shadow duration-200"
        >
          <div className="space-y-4">
            {availableFields.map(({ dataSource, fields }) => (
              <div key={dataSource.id}>
                <div className="font-medium text-gray-700 mb-2 flex items-center gap-2">
                  <Tag color="blue">{dataSource.type}</Tag>
                  {dataSource.name}
                </div>
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {fields.map(field => (
                    <div
                      key={field.name}
                      className="flex items-center justify-between p-2 bg-gray-50 rounded hover:bg-gray-100 transition-colors duration-200 cursor-pointer"
                    >
                      <div className="flex items-center gap-2">
                        <Checkbox
                          checked={isFieldSelected(dataSource.id, field.name)}
                          onChange={(e) => handleFieldChange(dataSource.id, field.name, e.target.checked)}
                          disabled={!isFieldSelected(dataSource.id, field.name) && selectedFields.length >= currentCardConfig.maxFields}
                        />
                        {fieldTypeIcons[field.type]}
                        <span className="text-sm">{field.name}</span>
                        {field.description && (
                          <span className="text-xs text-gray-500">({field.description})</span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}

            {availableFields.length === 0 && (
              <Empty description="暂无可选字段" />
            )}
          </div>
        </Card>

        {/* 配置选项 */}
        <Card
          title="配置选项"
          size="small"
          className="shadow-sm hover:shadow-md transition-shadow duration-200"
        >
          <Form
            form={form}
            layout="vertical"
            size="small"
            onValuesChange={() => {
              // 触发配置更新
              setTimeout(() => {
                const formValues = form.getFieldsValue();
                // 这里会触发useEffect中的配置更新
              }, 0);
            }}
          >
            {/* 基本配置 */}
            <Form.Item label="卡片标题" name="title">
              <Input placeholder="请输入卡片标题" />
            </Form.Item>

            {/* 图标配置 */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">显示图标</span>
                <Switch checked={showIcon} onChange={setShowIcon} size="small" />
              </div>

              {showIcon && (
                <div>
                  <div className="text-xs text-gray-500 mb-2">选择图标</div>
                  <div className="grid grid-cols-5 gap-2">
                    {availableIcons.map(iconItem => (
                      <Tooltip key={iconItem.key} title={iconItem.label}>
                        <Button
                          size="small"
                          type={selectedIcon === iconItem.key ? 'primary' : 'default'}
                          icon={iconItem.icon}
                          onClick={() => setSelectedIcon(iconItem.key)}
                        />
                      </Tooltip>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <Divider className="my-3" />

            {/* 说明配置 */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">显示说明</span>
                <Switch checked={showDescription} onChange={setShowDescription} size="small" />
              </div>

              {showDescription && (
                <Form.Item name="description">
                  <Input.TextArea
                    placeholder="请输入说明文字"
                    rows={2}
                    maxLength={100}
                    showCount
                  />
                </Form.Item>
              )}
            </div>

            <Divider className="my-3" />

            {/* 字体配置 */}
            <div className="space-y-3">
              <div className="text-sm font-medium">字体设置</div>

              <div className="grid grid-cols-2 gap-3">
                <Form.Item label="标题字号" name="titleFontSize">
                  <InputNumber min={12} max={24} defaultValue={16} size="small" />
                </Form.Item>
                <Form.Item label="标题颜色" name="titleFontColor">
                  <ColorPicker defaultValue="#1f1f1f" size="small" />
                </Form.Item>

                <Form.Item label="数值字号" name="valueFontSize">
                  <InputNumber min={16} max={36} defaultValue={24} size="small" />
                </Form.Item>
                <Form.Item label="数值颜色" name="valueFontColor">
                  <ColorPicker defaultValue="#1890ff" size="small" />
                </Form.Item>
              </div>
            </div>

            <Divider className="my-3" />

            {/* 样式配置 */}
            <div className="space-y-3">
              <div className="text-sm font-medium">样式设置</div>

              <div className="grid grid-cols-2 gap-3">
                <Form.Item label="背景色" name="backgroundColor">
                  <ColorPicker defaultValue="#ffffff" size="small" />
                </Form.Item>
                <Form.Item label="边框色" name="borderColor">
                  <ColorPicker defaultValue="#d9d9d9" size="small" />
                </Form.Item>

                <Form.Item label="圆角" name="borderRadius">
                  <InputNumber min={0} max={20} defaultValue={6} size="small" />
                </Form.Item>
              </div>
            </div>
          </Form>
        </Card>

        {/* 卡片预览 */}
        <Card
          title={
            <div className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <EyeOutlined className="text-green-500" />
                <span className="font-medium">卡片预览</span>
              </span>
              <Button
                type="text"
                size="small"
                onClick={() => setShowPreview(!showPreview)}
                className="hover:bg-gray-100 transition-colors"
              >
                {showPreview ? '隐藏' : '显示'}
              </Button>
            </div>
          }
          size="small"
          className="shadow-sm hover:shadow-md transition-shadow duration-200"
        >
          {showPreview && (
            <div className="h-80 animate-fadeIn">
              {(() => {
                // 获取当前表单值
                const formValues = form.getFieldsValue();

                const currentConfig: CardConfig = {
                  cardType,
                  fields: selectedFields,
                  showIcon,
                  icon: selectedIcon,
                  showDescription,
                  description: formValues.description || '这是一个示例描述',
                  titleFont: {
                    ...CARD_FONT_PRESETS.title,
                    size: formValues.titleFontSize || CARD_FONT_PRESETS.title.size,
                    color: formValues.titleFontColor || CARD_FONT_PRESETS.title.color
                  },
                  valueFont: {
                    ...CARD_FONT_PRESETS.value,
                    size: formValues.valueFontSize || CARD_FONT_PRESETS.value.size,
                    color: formValues.valueFontColor || CARD_FONT_PRESETS.value.color
                  },
                  descriptionFont: {
                    ...CARD_FONT_PRESETS.description,
                    size: formValues.descriptionFontSize || CARD_FONT_PRESETS.description.size,
                    color: formValues.descriptionFontColor || CARD_FONT_PRESETS.description.color
                  },
                  backgroundColor: formValues.backgroundColor || '#ffffff',
                  borderColor: formValues.borderColor || '#d9d9d9',
                  borderRadius: formValues.borderRadius || 6
                };

                // 检查是否有足够的字段
                if (selectedFields.length < currentCardConfig.minFields) {
                  return (
                    <div className="flex items-center justify-center h-full">
                      <div className="text-center text-gray-400">
                        <IdcardOutlined className="text-4xl mb-2" />
                        <div className="text-lg mb-1">请选择字段</div>
                        <div className="text-sm">
                          {currentCardConfig.label}需要至少{currentCardConfig.minFields}个字段
                        </div>
                      </div>
                    </div>
                  );
                }

                return <CardPreview config={currentConfig} />;
              })()}
            </div>
          )}

          {!showPreview && (
            <div className="flex items-center justify-center h-80 text-gray-400">
              <div className="text-center">
                <EyeOutlined className="text-4xl mb-2" />
                <div>点击"显示"查看卡片预览</div>
              </div>
            </div>
          )}
        </Card>
      </div>

      {/* 已选字段配置 */}
      {selectedFields.length > 0 && (
        <Card title="字段配置" size="small">
          <div className="space-y-3">
            {selectedFields.map(field => (
              <div key={field.id} className="flex items-center justify-between p-3 bg-gray-50 rounded border">
                <div className="flex items-center gap-3 flex-1">
                  <span className="font-medium">{field.name}</span>

                  <Select
                    value={field.aggregation}
                    onChange={(value) => handleFieldConfigChange(field.id, 'aggregation', value)}
                    options={aggregationOptions}
                    size="small"
                    style={{ width: 100 }}
                  />

                  <Input
                    placeholder="前缀"
                    value={field.prefix}
                    onChange={(e) => handleFieldConfigChange(field.id, 'prefix', e.target.value)}
                    size="small"
                    style={{ width: 80 }}
                  />

                  <Input
                    placeholder="后缀"
                    value={field.suffix}
                    onChange={(e) => handleFieldConfigChange(field.id, 'suffix', e.target.value)}
                    size="small"
                    style={{ width: 80 }}
                  />
                </div>

                <Button
                  type="text"
                  size="small"
                  icon={<DeleteOutlined />}
                  onClick={() => handleRemoveField(field.id)}
                  className="text-red-500 hover:text-red-700"
                />
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
};

export default CardConfigStep;
