import React from 'react';
import { Card, Statistic, Row, Col, Space } from 'antd';
import { 
  StarOutlined,
  HeartOutlined,
  TrophyOutlined,
  CrownOutlined,
  FireOutlined,
  <PERSON>boltOutlined,
  BulbOutlined,
  RocketOutlined,
  GiftOutlined
} from '@ant-design/icons';
import type { CardConfig, CardType, CardField } from '../types/component';

interface CardPreviewProps {
  config: CardConfig;
}

// 图标映射
const iconMap: Record<string, React.ReactNode> = {
  star: <StarOutlined />,
  heart: <HeartOutlined />,
  trophy: <TrophyOutlined />,
  crown: <CrownOutlined />,
  fire: <FireOutlined />,
  thunder: <ThunderboltOutlined />,
  bulb: <BulbOutlined />,
  rocket: <RocketOutlined />,
  gift: <GiftOutlined />
};

// 生成模拟数据
const generateMockValue = (field: CardField): string => {
  const { aggregation, prefix = '', suffix = '' } = field;
  
  let value: number;
  switch (aggregation) {
    case 'sum':
      value = Math.floor(Math.random() * 10000) + 1000;
      break;
    case 'count':
      value = Math.floor(Math.random() * 500) + 50;
      break;
    case 'avg':
      value = Math.floor(Math.random() * 100) + 10;
      break;
    case 'max':
      value = Math.floor(Math.random() * 1000) + 100;
      break;
    case 'min':
      value = Math.floor(Math.random() * 50) + 1;
      break;
    case 'first':
    case 'last':
      value = Math.floor(Math.random() * 100) + 10;
      break;
    default:
      value = Math.floor(Math.random() * 1000) + 100;
  }
  
  return `${prefix}${value.toLocaleString()}${suffix}`;
};

// 单卡片预览
const SingleCardPreview: React.FC<{ config: CardConfig }> = ({ config }) => {
  const field = config.fields[0];
  if (!field) return null;

  const mockValue = generateMockValue(field);
  
  return (
    <Card
      style={{
        backgroundColor: config.backgroundColor,
        borderColor: config.borderColor,
        borderRadius: config.borderRadius,
        textAlign: 'center',
        height: '100%'
      }}
      bodyStyle={{ padding: '24px 16px' }}
    >
      <Space direction="vertical" size="small" style={{ width: '100%' }}>
        {config.showIcon && config.icon && (
          <div style={{ fontSize: '32px', color: config.titleFont?.color }}>
            {iconMap[config.icon]}
          </div>
        )}
        
        <div
          style={{
            fontSize: config.titleFont?.size || 16,
            fontWeight: config.titleFont?.weight || 'normal',
            color: config.titleFont?.color || '#1f1f1f',
            marginBottom: '8px'
          }}
        >
          {field.name}
        </div>
        
        <div
          style={{
            fontSize: config.valueFont?.size || 24,
            fontWeight: config.valueFont?.weight || 'bold',
            color: config.valueFont?.color || '#1890ff',
            lineHeight: 1.2
          }}
        >
          {mockValue}
        </div>
        
        {config.showDescription && config.description && (
          <div
            style={{
              fontSize: config.descriptionFont?.size || 12,
              color: config.descriptionFont?.color || '#666666',
              marginTop: '8px'
            }}
          >
            {config.description}
          </div>
        )}
      </Space>
    </Card>
  );
};

// 双指标卡片预览
const DualCardPreview: React.FC<{ config: CardConfig }> = ({ config }) => {
  const fields = config.fields.slice(0, 2);
  if (fields.length < 2) return null;

  return (
    <Card
      style={{
        backgroundColor: config.backgroundColor,
        borderColor: config.borderColor,
        borderRadius: config.borderRadius,
        height: '100%'
      }}
      bodyStyle={{ padding: '16px' }}
    >
      <Row gutter={16} style={{ height: '100%' }}>
        {fields.map((field, index) => (
          <Col span={12} key={field.id}>
            <div style={{ textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
              {config.showIcon && config.icon && index === 0 && (
                <div style={{ fontSize: '24px', color: config.titleFont?.color, marginBottom: '8px' }}>
                  {iconMap[config.icon]}
                </div>
              )}
              
              <div
                style={{
                  fontSize: config.titleFont?.size || 14,
                  fontWeight: config.titleFont?.weight || 'normal',
                  color: config.titleFont?.color || '#1f1f1f',
                  marginBottom: '4px'
                }}
              >
                {field.name}
              </div>
              
              <div
                style={{
                  fontSize: config.valueFont?.size || 20,
                  fontWeight: config.valueFont?.weight || 'bold',
                  color: config.valueFont?.color || '#1890ff'
                }}
              >
                {generateMockValue(field)}
              </div>
            </div>
          </Col>
        ))}
      </Row>
      
      {config.showDescription && config.description && (
        <div
          style={{
            fontSize: config.descriptionFont?.size || 12,
            color: config.descriptionFont?.color || '#666666',
            textAlign: 'center',
            marginTop: '12px',
            borderTop: '1px solid #f0f0f0',
            paddingTop: '8px'
          }}
        >
          {config.description}
        </div>
      )}
    </Card>
  );
};

// 多指标卡片预览
const MultipleCardPreview: React.FC<{ config: CardConfig }> = ({ config }) => {
  const fields = config.fields;
  if (fields.length < 3) return null;

  // 计算网格布局
  const getGridLayout = (count: number) => {
    if (count <= 4) return { rows: 2, cols: 2 };
    if (count <= 6) return { rows: 2, cols: 3 };
    return { rows: 3, cols: 3 };
  };

  const { rows, cols } = getGridLayout(fields.length);
  const colSpan = 24 / cols;

  return (
    <Card
      style={{
        backgroundColor: config.backgroundColor,
        borderColor: config.borderColor,
        borderRadius: config.borderRadius,
        height: '100%'
      }}
      bodyStyle={{ padding: '12px' }}
    >
      {config.showIcon && config.icon && (
        <div style={{ textAlign: 'center', marginBottom: '12px' }}>
          <span style={{ fontSize: '20px', color: config.titleFont?.color }}>
            {iconMap[config.icon]}
          </span>
        </div>
      )}
      
      <div style={{ display: 'grid', gridTemplateColumns: `repeat(${cols}, 1fr)`, gap: '8px', height: 'calc(100% - 40px)' }}>
        {fields.slice(0, 8).map((field) => (
          <div
            key={field.id}
            style={{
              textAlign: 'center',
              padding: '8px 4px',
              backgroundColor: 'rgba(0,0,0,0.02)',
              borderRadius: '4px',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center'
            }}
          >
            <div
              style={{
                fontSize: config.titleFont?.size ? Math.max(config.titleFont.size - 2, 10) : 12,
                fontWeight: config.titleFont?.weight || 'normal',
                color: config.titleFont?.color || '#1f1f1f',
                marginBottom: '2px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}
            >
              {field.name}
            </div>
            
            <div
              style={{
                fontSize: config.valueFont?.size ? Math.max(config.valueFont.size - 4, 14) : 16,
                fontWeight: config.valueFont?.weight || 'bold',
                color: config.valueFont?.color || '#1890ff'
              }}
            >
              {generateMockValue(field)}
            </div>
          </div>
        ))}
      </div>
      
      {config.showDescription && config.description && (
        <div
          style={{
            fontSize: config.descriptionFont?.size || 10,
            color: config.descriptionFont?.color || '#666666',
            textAlign: 'center',
            marginTop: '8px',
            borderTop: '1px solid #f0f0f0',
            paddingTop: '6px'
          }}
        >
          {config.description}
        </div>
      )}
    </Card>
  );
};

const CardPreview: React.FC<CardPreviewProps> = ({ config }) => {
  // 根据卡片类型渲染不同的预览
  switch (config.cardType) {
    case 'single':
      return <SingleCardPreview config={config} />;
    case 'dual':
      return <DualCardPreview config={config} />;
    case 'multiple':
      return <MultipleCardPreview config={config} />;
    default:
      return (
        <Card style={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <div style={{ textAlign: 'center', color: '#999' }}>
            <div>暂不支持此卡片类型预览</div>
          </div>
        </Card>
      );
  }
};

export default CardPreview;
