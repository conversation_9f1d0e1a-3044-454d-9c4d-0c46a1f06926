import { useState, useEffect } from 'react';
import { <PERSON>, Tree, Card, Button, Tag, Empty, Divider, Space, Tooltip, Alert } from 'antd';
import {
  BarChartOutlined,
  AreaChartOutlined,
  LineChartOutlined,
  PieChartOutlined,
  PartitionOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON>hartOutlined,
  Funnel<PERSON>lotOutlined,
  StockOutlined,
  ShareAltOutlined,
  CloudOutlined,
  DeleteOutlined,
  DatabaseOutlined,
  FieldStringOutlined,
  FieldNumberOutlined,
  FieldTimeOutlined,
  FieldBinaryOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { DndContext, DragEndEvent, DragOverlay, useDraggable, useDroppable } from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import type {
  ChartType,
  ChartConfig,
  DimensionMetricField,
  DataSourceField,
  ChartTypeConfig
} from '../types/component';
import { CHART_TYPE_CONFIGS, CHART_TYPE_OPTIONS } from '../types/component';
import { mockDataSources } from '../mock/componentData';
import EChart from './charts/EChart';
import { generateChartOptions, validateChartConfig } from '../utils/chartPreview';

interface ChartConfigStepProps {
  selectedDataSource: string;
  relatedDataSources: string[];
  value?: ChartConfig;
  onChange?: (config: ChartConfig) => void;
}

// 图表类型图标映射
const chartTypeIcons: Record<ChartType, React.ReactNode> = {
  bar: <BarChartOutlined />,
  histogram: <AreaChartOutlined />,
  line: <LineChartOutlined />,
  area: <AreaChartOutlined />,
  pie: <PieChartOutlined />,
  doughnut: <PieChartOutlined />,
  tree: <PartitionOutlined />,
  scatter: <DotChartOutlined />,
  radar: <RadarChartOutlined />,
  funnel: <FunnelPlotOutlined />,
  combo: <StockOutlined />,
  graph: <ShareAltOutlined />,
  wordcloud: <CloudOutlined />
};

// 字段类型图标映射
const fieldTypeIcons: Record<string, React.ReactNode> = {
  string: <FieldStringOutlined className="text-blue-500" />,
  number: <FieldNumberOutlined className="text-green-500" />,
  date: <FieldTimeOutlined className="text-orange-500" />,
  boolean: <FieldBinaryOutlined className="text-purple-500" />,
  array: <FieldStringOutlined className="text-gray-500" />
};

// 可拖拽字段组件
const DraggableField: React.FC<{ field: DataSourceField; dataSourceId: string }> = ({ field, dataSourceId }) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: `${dataSourceId}-${field.name}`,
    data: { field, dataSourceId }
  });

  const style = {
    transform: CSS.Translate.toString(transform),
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...listeners}
      {...attributes}
      className={`drag-item flex items-center gap-2 p-2 bg-gray-50 rounded border cursor-move hover:bg-gray-100 transition-all duration-200 ${isDragging ? 'shadow-lg scale-105' : 'hover:shadow-md'
        }`}
    >
      {fieldTypeIcons[field.type]}
      <span className="text-sm">{field.name}</span>
      {field.description && (
        <span className="text-xs text-gray-500">({field.description})</span>
      )}
    </div>
  );
};

// 可放置区域组件
const DroppableArea: React.FC<{
  id: string;
  title: string;
  fields: DimensionMetricField[];
  onRemove: (fieldId: string) => void;
  maxFields?: number;
}> = ({ id, title, fields, onRemove, maxFields }) => {
  const { setNodeRef, isOver } = useDroppable({ id });

  return (
    <div
      ref={setNodeRef}
      className={`min-h-[120px] p-3 border-2 border-dashed rounded-lg transition-all duration-200 ${isOver
        ? 'drag-over border-blue-500 bg-gradient-to-br from-blue-50 to-indigo-50 shadow-lg'
        : 'border-gray-300 bg-gray-50 hover:border-gray-400 hover:bg-gray-100'
        }`}
    >
      <div className="flex items-center justify-between mb-2">
        <span className="font-medium text-gray-700">{title}</span>
        {maxFields && (
          <span className="text-xs text-gray-500">
            {fields.length}/{maxFields}
          </span>
        )}
      </div>

      {fields.length > 0 ? (
        <div className="space-y-2">
          {fields.map(field => (
            <div key={field.id} className="flex items-center justify-between p-2 bg-white rounded border">
              <div className="flex items-center gap-2">
                {fieldTypeIcons[field.fieldName] || <FieldStringOutlined />}
                <span className="text-sm font-medium">{field.name}</span>
                {field.aggregation && (
                  <Tag color="blue" className="text-xs">{field.aggregation}</Tag>
                )}
              </div>
              <Button
                type="text"
                size="small"
                icon={<DeleteOutlined />}
                onClick={() => onRemove(field.id)}
                className="text-red-500 hover:text-red-700"
              />
            </div>
          ))}
        </div>
      ) : (
        <div className="flex items-center justify-center h-16 text-gray-400 text-sm">
          拖拽字段到此处
        </div>
      )}
    </div>
  );
};

const ChartConfigStep: React.FC<ChartConfigStepProps> = ({
  selectedDataSource,
  relatedDataSources,
  value,
  onChange
}) => {
  const [chartType, setChartType] = useState<ChartType>(value?.chartType || 'bar');
  const [dimensions, setDimensions] = useState<DimensionMetricField[]>(value?.dimensions || []);
  const [metrics, setMetrics] = useState<DimensionMetricField[]>(value?.metrics || []);
  const [activeId, setActiveId] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState<boolean>(true);

  // 获取所有相关数据源的字段
  const allDataSources = [selectedDataSource, ...relatedDataSources];
  const fieldsTreeData = allDataSources.map(dsId => {
    const ds = mockDataSources.find(d => d.id === dsId);
    if (!ds) return null;

    return {
      title: ds.name,
      key: dsId,
      icon: <DatabaseOutlined className="text-blue-500" />,
      children: ds.fields?.map(field => ({
        title: (
          <DraggableField field={field} dataSourceId={dsId} />
        ),
        key: `${dsId}-${field.name}`,
        isLeaf: true,
        field,
        dataSourceId: dsId
      })) || []
    };
  }).filter(Boolean);

  // 获取当前图表类型配置
  const currentChartConfig = CHART_TYPE_CONFIGS[chartType];

  // 处理图表类型变化
  const handleChartTypeChange = (type: ChartType) => {
    setChartType(type);
    // 清空不符合新图表类型要求的字段
    const config = CHART_TYPE_CONFIGS[type];
    if (dimensions.length > (config.maxDimensions || 999)) {
      setDimensions(dimensions.slice(0, config.maxDimensions));
    }
    if (metrics.length > (config.maxMetrics || 999)) {
      setMetrics(metrics.slice(0, config.maxMetrics));
    }
  };

  // 处理拖拽结束
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveId(null);

    if (!over) return;

    const dragData = active.data.current;
    const dropZone = over.id as string;

    if (!dragData) return;

    const { field, dataSourceId } = dragData;
    const newField: DimensionMetricField = {
      id: `${dataSourceId}-${field.name}-${Date.now()}`,
      name: field.name,
      type: dropZone === 'dimensions' ? 'dimension' : 'metric',
      fieldName: field.name,
      dataSourceId,
      aggregation: dropZone === 'metrics' ? 'sum' : undefined
    };

    if (dropZone === 'dimensions') {
      if (dimensions.length < (currentChartConfig.maxDimensions || 999)) {
        setDimensions([...dimensions, newField]);
      }
    } else if (dropZone === 'metrics') {
      if (metrics.length < (currentChartConfig.maxMetrics || 999)) {
        setMetrics([...metrics, newField]);
      }
    }
  };

  // 移除字段
  const handleRemoveField = (fieldId: string, type: 'dimension' | 'metric') => {
    if (type === 'dimension') {
      setDimensions(dimensions.filter(f => f.id !== fieldId));
    } else {
      setMetrics(metrics.filter(f => f.id !== fieldId));
    }
  };

  // 更新配置
  useEffect(() => {
    const config: ChartConfig = {
      chartType,
      dimensions,
      metrics,
      title: `${currentChartConfig.label}图表`,
      showLegend: true,
      showDataLabels: false
    };
    onChange?.(config);
  }, [chartType, dimensions, metrics, currentChartConfig, onChange]);

  return (
    <div className="space-y-6 max-h-full">
      {/* 图表类型选择 */}
      <Card title="选择图表类型" size="small">
        <Radio.Group
          value={chartType}
          onChange={(e) => handleChartTypeChange(e.target.value)}
          className="w-full"
        >
          <div className="grid grid-cols-4 gap-3">
            {CHART_TYPE_OPTIONS.map(config => (
              <Radio.Button
                key={config.type}
                value={config.type}
                className="flex flex-col items-center p-3 h-auto"
              >
                <div className="text-lg mb-1">
                  {chartTypeIcons[config.type]}
                </div>
                <span className="text-xs">{config.label}</span>
              </Radio.Button>
            ))}
          </div>
        </Radio.Group>

        {/* 图表类型说明 */}
        <div className="mt-3 p-3 bg-blue-50 rounded">
          <div className="text-sm text-blue-700">
            <strong>{currentChartConfig.label}</strong>: {currentChartConfig.description}
          </div>
          <div className="text-xs text-blue-600 mt-1">
            需要维度: {currentChartConfig.requiredDimensions}-{currentChartConfig.maxDimensions || '∞'} 个，
            需要指标: {currentChartConfig.requiredMetrics}-{currentChartConfig.maxMetrics || '∞'} 个
          </div>
        </div>
      </Card>

      {/* 字段配置与预览 */}
      <div className="grid grid-cols-2 gap-6">
        {/* 左侧：字段配置 */}
        <div className="space-y-4">
          <DndContext onDragEnd={handleDragEnd} onDragStart={(event) => setActiveId(event.active.id as string)}>
            <div className="grid grid-cols-1 gap-4">
              {/* 数据源字段 */}
              <Card title="数据源字段" size="small">
                {fieldsTreeData.length > 0 ? (
                  <Tree
                    treeData={fieldsTreeData}
                    showLine={{ showLeafIcon: false }}
                    defaultExpandAll
                    className="max-h-60 overflow-y-auto"
                  />
                ) : (
                  <Empty description="暂无数据源字段" />
                )}
              </Card>

              {/* 维度和指标配置 */}
              <div className="grid grid-cols-2 gap-3">
                <DroppableArea
                  id="dimensions"
                  title="维度"
                  fields={dimensions}
                  onRemove={(id) => handleRemoveField(id, 'dimension')}
                  maxFields={currentChartConfig.maxDimensions}
                />

                <DroppableArea
                  id="metrics"
                  title="指标"
                  fields={metrics}
                  onRemove={(id) => handleRemoveField(id, 'metric')}
                  maxFields={currentChartConfig.maxMetrics}
                />
              </div>
            </div>

            {/* 拖拽预览 */}
            <DragOverlay>
              {activeId ? (
                <div className="p-2 bg-white border rounded shadow-lg">
                  拖拽中...
                </div>
              ) : null}
            </DragOverlay>
          </DndContext>
        </div>

        {/* 右侧：图表预览 */}
        <div className="space-y-4">
          <Card
            title={
              <div className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <EyeOutlined />
                  图表预览
                </span>
                <Button
                  type="text"
                  size="small"
                  onClick={() => setShowPreview(!showPreview)}
                >
                  {showPreview ? '隐藏' : '显示'}
                </Button>
              </div>
            }
            size="small"
          >
            {showPreview && (
              <div className="h-80">
                {(() => {
                  const currentConfig: ChartConfig = {
                    chartType,
                    dimensions,
                    metrics,
                    title: `${currentChartConfig.label}图表`,
                    showLegend: true,
                    showDataLabels: false
                  };

                  const validation = validateChartConfig(currentConfig);

                  if (!validation.isValid) {
                    return (
                      <div className="flex items-center justify-center h-full">
                        <Alert
                          message="配置不完整"
                          description={validation.message}
                          type="warning"
                          showIcon
                        />
                      </div>
                    );
                  }

                  const chartOptions = generateChartOptions(chartType, currentConfig);

                  if (!chartOptions) {
                    return (
                      <div className="flex items-center justify-center h-full">
                        <Alert
                          message="暂无预览"
                          description="当前图表类型暂不支持预览"
                          type="info"
                          showIcon
                        />
                      </div>
                    );
                  }

                  return (
                    <EChart
                      options={chartOptions}
                    />
                  );
                })()}
              </div>
            )}

            {!showPreview && (
              <div className="flex items-center justify-center h-80 text-gray-400">
                <div className="text-center">
                  <EyeOutlined className="text-4xl mb-2" />
                  <div>点击"显示"查看图表预览</div>
                </div>
              </div>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ChartConfigStep;
