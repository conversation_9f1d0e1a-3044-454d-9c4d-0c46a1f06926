import { Key, ReactNode, useEffect, useState } from 'react';
import { Tabs, Button, Input, Tree, Modal, message, Dropdown, Space, Flex, CollapseProps, Collapse, Spin, Alert, Empty } from 'antd';
import { MoreOutlined, PlusOutlined, SearchOutlined, ExclamationCircleOutlined, DeleteOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import WorkspaceDataPreview from '../components/WorkspaceDataPreview';
import RelationView from '../components/RelationView';
// import FormulaEditorModal from '../components/FormulaEditorModal';
import DatabaseTableCreateModal from '../components/DatabaseTableCreateModal';
import DatabaseTableEditModal from '../components/DatabaseTableEditModal';
import { CustomTreeNode, DatabaseInfo, Dataset } from '../types';
import { findNodeByKey, tableMenu, updateNodeByKey } from '../utils';
import { useNavigate } from 'react-router';
import { trpc } from '../utils/tRPC';

// 构建树形数据的函数 - 处理 Record<string, Dataset[]> 格式
const buildTreeFromTables = (rawData: Record<string, Dataset[]>, targetKey: string): CustomTreeNode[] => {
	if (!rawData[targetKey]) return [];

	const result: CustomTreeNode[] = [];
	const datasets = rawData[targetKey];

	datasets.forEach(dataset => {
		// 创建数据集文件夹节点
		const datasetFolder: CustomTreeNode = {
			title: dataset.name,
			key: `dataset-${dataset.name}`,
			type: 'folder',
			editable: false,
			usedInWorkspace: false,
			children: [],
		};

		// 添加该数据集下的所有表作为子节点
		dataset.data_entities.forEach(entity => {
			datasetFolder.children!.push({
				title: entity.name,
				key: entity.id,
				type: 'table',
				tableType: 'db',
				editable: false,
				usedInWorkspace: false,
				description: entity.description,
				collectorName: dataset.name,
				isLeaf: true,
			});
		});
		result.push(datasetFolder);
	});

	return result;
};
const removeNode = (nodes: CustomTreeNode[], key: string): CustomTreeNode[] => {
	return nodes.filter(node => {
		if (node.key === key) return false;
		if (node.children) node.children = removeNode(node.children, key);
		return true;
	});
};
const PublicWorkSpace = () => {
	const [activeTab, setActiveTab] = useState('dataPreview');
	const [activeTree, setActiveTree] = useState('data');
	const [expandedKeys, setExpandedKeys] = useState<Key[]>([]);
	const [selectedKey, setSelectedKey] = useState<Key>('');
	const [selectedEntityId, setSelectedEntityId] = useState<string>('');
	// 视图相关状态
	const [viewExpandedKeys, setViewExpandedKeys] = useState<Key[]>([]);
	const [viewSelectedKeys, setViewSelectedKeys] = useState<Key[]>([]);
	const [addKey, setAddKey] = useState('')
	const navigate = useNavigate()
	const [editingKey, setEditingKey] = useState<string | null>(null);
	const [editingValue, setEditingValue] = useState<string>('');
	const [createDbModalOpen, setCreateDbModalOpen] = useState(false);
	const [editDbModalOpen, setEditDbModalOpen] = useState(false);
	// const trpc = useTRPC();
	// 使用tRPC查询数据表，并进行后置处理
	const { data: tablesData, isLoading: tablesLoading, error: tablesError } = useQuery({
		...trpc.dataSource.listTables.queryOptions({ workspaceId: null }),
		select: (data) => {
			// 拆分
			return {
				collectorTreeData: buildTreeFromTables(data, '采集器'),
				databaseTreeData: buildTreeFromTables(data, '数据库'),
				localFileTreeData: buildTreeFromTables(data, '本地文件'),
				originalData: data
			};
		}
	});

	// 使用tRPC查询视图数据，并进行后置处理
	const { data: viewsData, isLoading: viewsLoading, error: viewsError } = useQuery({
		...trpc.dataSource.listViews.queryOptions({ workspaceId: null }),
		select: (data) => data.map(dataset => ({
			title: dataset.name,
			key: `view-${dataset.name}`,
			type: 'folder',
			editable: false,
			usedInWorkspace: false,
			children: dataset.data_entities.map(entity => ({
				title: entity.name,
				key: entity.id,
				type: 'table',
				editable: false,
				usedInWorkspace: false,
				description: entity.description,
				collectorName: dataset.name,
			})),
		}))
	});

	// 四个独立的状态管理
	const [collectorTreeData, setCollectorTreeData] = useState<CustomTreeNode[]>([]);
	const [databaseTreeData, setDatabaseTreeData] = useState<CustomTreeNode[]>([]);
	const [localFileTreeData, setLocalFileTreeData] = useState<CustomTreeNode[]>([]);
	const [viewTreeData, setViewTreeData] = useState<CustomTreeNode[]>([]);

	// 当后端数据加载完成时更新状态
	useEffect(() => {
		if (tablesData) {
			setCollectorTreeData(tablesData.collectorTreeData || []);
			setDatabaseTreeData(tablesData.databaseTreeData || []);
			setLocalFileTreeData(tablesData.localFileTreeData || []);
		}
	}, [tablesData]);

	// 当视图数据加载完成时更新状态
	useEffect(() => {
		if (viewsData) {
			setViewTreeData(viewsData);
		}
	}, [viewsData]);

	// 创建数据源映射和通用更新函数
	const dataSourceMap = {
		collector: { data: collectorTreeData, setter: setCollectorTreeData },
		database: { data: databaseTreeData, setter: setDatabaseTreeData },
		localFile: { data: localFileTreeData, setter: setLocalFileTreeData },
		view: { data: viewTreeData, setter: setViewTreeData },
	};

	// 通用的节点查找和状态更新函数
	const findNodeAndUpdate = (key: string, updateFn: (prev: CustomTreeNode[]) => CustomTreeNode[]) => {
		for (const [sourceKey, source] of Object.entries(dataSourceMap)) {
			if (findNodeByKey(source.data, key)) {
				source.setter(updateFn);
				return true;
			}
		}
		return false;
	};

	// 通用的节点查找函数
	const findNodeInAllSources = (key: string): CustomTreeNode | null => {
		for (const source of Object.values(dataSourceMap)) {
			const node = findNodeByKey(source.data, key);
			if (node) return node;
		}
		return null;
	};

	const [editDbInitialValues, setEditDbInitialValues] = useState<DatabaseInfo>({
		name: '',
		type: '',
		host: '',
		port: 0,
		username: '',
		password: ''
	});

	// 搜索相关状态
	const [dataSearchValue, setDataSearchValue] = useState('');
	const [viewSearchValue, setViewSearchValue] = useState('');

	// 搜索过滤函数
	const filterTreeData = (treeData: CustomTreeNode[], searchValue: string): CustomTreeNode[] => {
		if (!searchValue.trim()) return treeData;

		const filterNode = (node: CustomTreeNode): CustomTreeNode | null => {
			const title = typeof node.title === 'string' ? node.title : '';
			const matchesSearch = title.toLowerCase().includes(searchValue.toLowerCase());

			// 过滤子节点
			const filteredChildren = node.children
				? node.children.map(filterNode).filter(Boolean) as CustomTreeNode[]
				: [];

			// 如果当前节点匹配或有匹配的子节点，则保留
			if (matchesSearch || filteredChildren.length > 0) {
				return {
					...node,
					children: filteredChildren.length > 0 ? filteredChildren : node.children
				};
			}

			return null;
		};

		return treeData.map(filterNode).filter(Boolean) as CustomTreeNode[];
	};
	const handleAddDataTable = (folderKey: string) => {
		const newTable: CustomTreeNode = {
			title: `新表-数据库`,
			key: `${folderKey}-new-${Date.now()}`,
			type: 'table',
			tableType: 'db',
			editable: false,
			usedInWorkspace: false,
		};

		// 使用通用函数添加表格
		findNodeAndUpdate(folderKey, prev => updateNodeByKey(prev, folderKey, node => {
			node.children = node.children ? [...node.children, newTable] : [newTable];
		}));

		setAddKey('')
	}
	// 视图选择处理函数
	const handleViewSelect = (selectedKeys: React.Key[]) => {
		setViewSelectedKeys(selectedKeys);

		// 如果选中的是表节点，也设置 selectedEntityId
		const key = selectedKeys[0] as string;
		if (key) {
			const node = findNodeByKey(viewTreeData, key);
			if (node && node.type === 'table') {
				setSelectedEntityId(key);
			} else {
				setSelectedEntityId('');
			}
		} else {
			setSelectedEntityId('');
		}
	};

	const handleTableSelect = (selectedKeys: React.Key[]) => {
		const key = selectedKeys[0] as string;
		setSelectedKey(key);

		// 查找选中的节点，如果是表类型且有 entity_id，则设置为 selectedEntityId
		const node = findNodeInAllSources(key);
		if (node && node.type === 'table' && key) {
			// 对于表节点，key 就是 entity_id
			setSelectedEntityId(key);
		} else {
			setSelectedEntityId('');
		}
	};
	const handleTabChange = (key: string) => {
		setActiveTab(key);
	};

	// 重命名保存
	const handleRenameSave = (key: string) => {
		if (!editingValue.trim()) {
			message.warning('名称不能为空');
			return;
		}

		// 使用通用函数更新节点
		findNodeAndUpdate(key, prev => updateNodeByKey(prev, key, node => {
			node.title = editingValue;
			node.editable = false;
		}));

		setEditingKey(null);
		setEditingValue('');
	};
	// 视图重命名
	const handleRenameView = (key: string) => {
		if (!editingValue.trim()) {
			message.warning('名称不能为空');
			return;
		}

		// 使用通用函数更新节点
		findNodeAndUpdate(key, prev => updateNodeByKey(prev, key, node => {
			node.title = editingValue;
			node.editable = false;
		}));

		setEditingKey(null);
		setEditingValue('');
	};
	const removeNode = (nodes: CustomTreeNode[], key: string): CustomTreeNode[] => {
		return nodes.filter(node => {
			if (node.key === key) return false;
			if (node.children) node.children = removeNode(node.children, key);
			return true;
		});
	};
	// 视图删除
	const handleDeleteView = (key: string) => {
		// 使用通用函数查找节点
		const node = findNodeInAllSources(key);
		if (!node) return;
		if (node.type === 'folder') {
			const hasUsed = (node.children || []).some(child => child.usedInWorkspace);
			if (hasUsed) {
				Modal.warning({
					title: '无法删除',
					content: '当前文件夹中有表在工作区中被使用，无法删除！',
				});
				return;
			}
		}
		if (node.type === 'table' && node.usedInWorkspace) {
			Modal.warning({
				title: '无法删除',
				content: '当前表在工作区被使用，请先删除工作区中相关表，再删除该表！',
			});
			return;
		}

		Modal.confirm({
			title: '确认删除',
			content: `是否删除${node.type === 'folder' ? '文件夹' : '表'}"${typeof node.title === 'string' ? node.title : ''}"？`,
			onOk: () => {
				// 使用通用函数删除节点
				findNodeAndUpdate(key, prev => removeNode(prev, key));
			},
		});
	};
	//删除数据文件夹
	const handleDelete = (key: string) => {
		// 使用通用函数查找节点
		const node = findNodeInAllSources(key);
		if (!node) return;
		if (node.type === 'folder') {
			const hasUsed = (node.children || []).some(child => child.usedInWorkspace);
			if (hasUsed) {
				Modal.warning({
					title: '无法删除',
					content: '当前文件夹中有表在工作区中被使用，无法删除！',
				});
				return;
			}
		}
		if (node.type === 'table' && node.usedInWorkspace) {
			Modal.warning({
				title: '无法删除',
				content: '当前表在工作区被使用，请先删除工作区中相关表，再删除该表！',
			});
			return;
		}
		Modal.confirm({
			title: '确认删除',
			content: `是否删除${node.type === 'folder' ? '文件夹' : '表'}"${typeof node.title === 'string' ? node.title : ''}"？`,
			onOk: () => {
				// 使用通用函数删除节点
				findNodeAndUpdate(key, prev => removeNode(prev, key));
			},
		});
	};

	const handleAddTable = (folderKey: string, type: 'db' | 'excel') => {
		if (type === 'db') {
			setCreateDbModalOpen(true)
		} else {
			// 跳转到文件上传页面
			navigate('/publicWorkspace/upload')
		}
		setAddKey(folderKey)
	};
	const handleMoreActions = (nodeData: CustomTreeNode, type: 'rename' | 'delete') => {
		if (type === 'rename') {
			setEditingKey(nodeData.key as string);
			setEditingValue(typeof nodeData.title === 'string' ? nodeData.title : '');
		} else {
			handleDelete(nodeData.key as string)
		}
	}
	const handleMoreViewActions = (nodeData: CustomTreeNode, type: 'rename' | 'delete') => {
		if (type === 'rename') {
			setEditingKey(nodeData.key as string);
			setEditingValue(typeof nodeData.title === 'string' ? nodeData.title : '');
		} else {
			handleDeleteView(nodeData.key as string)
		}
	}
	// titleRender实现
	const renderCustomNodeTitle = (nodeData: CustomTreeNode, tableType: 'db' | 'excel') => {
		if (editingKey === nodeData.key) {
			return (
				<Input
					autoFocus
					size="small"
					value={editingValue}
					onChange={e => setEditingValue(e.target.value)}
					onBlur={() => handleRenameSave(nodeData.key as string)}
					onPressEnter={() => handleRenameSave(nodeData.key as string)}
					className="w-full"
				/>
			);
		}
		// 文件夹节点
		if (nodeData.type === 'folder') {
			return (
				<div className="flex justify-between">
					<span onDoubleClick={() => {
						setEditingKey(nodeData.key as string);
						setEditingValue(typeof nodeData.title === 'string' ? nodeData.title : '');
					}}>{nodeData.title as ReactNode}</span>
					<span className='gap-2'>
						<Button className='inline' type="text" size="small" onClick={e => { e.stopPropagation(); handleAddTable(nodeData.key as string, tableType); }} icon={<PlusOutlined />} />
						<Button className='inline' type="text" size="small" onClick={e => { e.stopPropagation(); handleDelete(nodeData.key as string); }} icon={<DeleteOutlined />} />
					</span>
				</div>
			);
		}
		// 表节点
		if (nodeData.type === 'table') {
			return (
				<div className="flex items-center group justify-between">
					<span
						style={{
							color: nodeData.usedInWorkspace ? '#faad14' : undefined,
							fontWeight: nodeData.usedInWorkspace ? 600 : undefined,
							marginRight: 4,
						}}
						onDoubleClick={() => {
							setEditingKey(nodeData.key as string);
							setEditingValue(typeof nodeData.title === 'string' ? nodeData.title : '');
						}}
					>
						{nodeData.title as ReactNode}
						{nodeData.tableType ? <span style={{ fontSize: 12, color: '#888', marginLeft: 4 }}>({nodeData.tableType})</span> : null}
						{nodeData.usedInWorkspace && <ExclamationCircleOutlined style={{ color: '#faad14', marginLeft: 4 }} title="被工作区使用" />}
					</span>
					<span className="invisible group-hover:visible">
						<Dropdown
							menu={{
								items: tableMenu,
								onClick: ({ key }) => handleMoreActions(nodeData, key as 'rename' | 'delete')
							}}
							trigger={['hover']}
						>
							<MoreOutlined className="ml-2 cursor-pointer" />
						</Dropdown>
					</span>
				</div>
			);
		}
		// 其他情况
		return <span>{nodeData.title as ReactNode}</span>;
	};
	const renderCustomViewTitle = (nodeData: CustomTreeNode) => {
		if (editingKey === nodeData.key) {
			return (
				<Input
					autoFocus
					size="small"
					value={editingValue}
					onChange={e => setEditingValue(e.target.value)}
					onBlur={() => handleRenameView(nodeData.key as string)}
					onPressEnter={() => handleRenameView(nodeData.key as string)}
					className="w-full"
				/>
			);
		}
		// 文件夹节点
		if (nodeData.type === 'folder') {
			return (
				<div className="flex items-center justify-between">
					<span onDoubleClick={() => {
						setEditingKey(nodeData.key as string);
						setEditingValue(typeof nodeData.title === 'string' ? nodeData.title : '');
					}}>{nodeData.title as ReactNode}</span>
					<Button type="text" size="small" onClick={e => { e.stopPropagation(); handleDeleteView(nodeData.key as string); }} icon={<DeleteOutlined />} />
				</div>
			);
		}
		// 表节点
		if (nodeData.type === 'table') {
			return (
				<div className="flex group items-center justify-between">
					<span
						style={{
							color: nodeData.usedInWorkspace ? '#faad14' : undefined,
							fontWeight: nodeData.usedInWorkspace ? 600 : undefined,
							marginRight: 4,
						}}
						onDoubleClick={() => {
							setEditingKey(nodeData.key as string);
							setEditingValue(typeof nodeData.title === 'string' ? nodeData.title : '');
						}}
					>
						{nodeData.title as ReactNode}
						{nodeData.tableType ? <span style={{ fontSize: 12, color: '#888', marginLeft: 4 }}>({nodeData.tableType})</span> : null}
						{nodeData.usedInWorkspace && <ExclamationCircleOutlined style={{ color: '#faad14', marginLeft: 4 }} title="被工作区使用" />}
					</span>
					<span className="invisible group-hover:visible">
						<Dropdown
							menu={{
								items: tableMenu,
								onClick: ({ key }) => handleMoreViewActions(nodeData, key as 'rename' | 'delete')
							}}
							trigger={['hover']}
						>
							<MoreOutlined className="ml-2 cursor-pointer" />
						</Dropdown>
					</span>
				</div>
			);
		}
		// 其他情况
		return <span>{nodeData.title as ReactNode}</span>;
	};
	// 过滤后的树数据
	const filteredCollectorTreeData = filterTreeData(collectorTreeData, dataSearchValue);
	const filteredDatabaseTreeData = filterTreeData(databaseTreeData, dataSearchValue);
	const filteredLocalFileTreeData = filterTreeData(localFileTreeData, dataSearchValue);
	const filteredViewTreeData = filterTreeData(viewTreeData, viewSearchValue);

	const collapseItems: CollapseProps['items'] = [
		{
			key: 'get',
			label: '采集器',
			children: filteredCollectorTreeData.length > 0 ? <Tree
				treeData={filteredCollectorTreeData}
				expandedKeys={expandedKeys}
				onSelect={handleTableSelect}
				onExpand={setExpandedKeys}
				// showLine
				blockNode
			/> : <Empty description={dataSearchValue ? '未找到匹配的数据' : '暂无数据'} />,
		},
		{
			key: 'db',
			label: '数据库',
			children: filteredDatabaseTreeData.length > 0 ? <Tree
				treeData={filteredDatabaseTreeData}
				expandedKeys={expandedKeys}
				onSelect={handleTableSelect}
				onExpand={setExpandedKeys}
				// showLine
				blockNode
				titleRender={(nodeData) => renderCustomNodeTitle(nodeData, 'db')}
			/> : <Empty description={dataSearchValue ? '未找到匹配的数据' : '暂无数据'} />,
		},
		{
			key: 'excel',
			label: '本地文件',
			children: filteredLocalFileTreeData.length > 0 ? <Tree
				treeData={filteredLocalFileTreeData}
				expandedKeys={expandedKeys}
				onSelect={handleTableSelect}
				onExpand={setExpandedKeys}
				// showLine
				blockNode
				titleRender={(nodeData) => renderCustomNodeTitle(nodeData, 'excel')}
			/> : <Empty description={dataSearchValue ? '未找到匹配的数据' : '暂无数据'} />,
		},
	];

	// 新建文件夹功能
	const handleAddFolder = () => {
		// 统计已有文件夹数量
		const folderCount = localFileTreeData.filter(node => node.type === 'folder').length;
		const newFolder = {
			title: `文件夹${folderCount + 1}`,
			key: `folder${Date.now()}`,
			type: 'folder',
			editable: false,
			usedInWorkspace: false,
			children: [],
		};
		setLocalFileTreeData(prev => [...prev, newFolder]);
	};

	// 新建视图文件夹功能
	const handleAddViewFolder = () => {
		const folderCount = viewTreeData.filter(node => node.type === 'folder').length;
		const newFolder: CustomTreeNode = {
			title: `文件夹${folderCount + 1}`,
			key: `viewFolder${Date.now()}`,
			type: 'folder',
			editable: false,
			usedInWorkspace: false,
			children: [],
		};
		setViewTreeData(prev => [...prev, newFolder]);
	};

	// 显示加载状态
	if (tablesLoading || viewsLoading) {
		return (
			<div className="flex h-[calc(100vh-64px)] items-center justify-center">
				<Spin size="large" />
			</div>
		);
	}

	// 显示错误状态
	if (tablesError || viewsError) {
		return (
			<div className="flex h-[calc(100vh-64px)] items-center justify-center">
				<Alert
					message="数据加载失败"
					description={tablesError?.message || viewsError?.message || '数据加载失败'}
					type="error"
					showIcon
				/>
			</div>
		);
	}

	return (
		<div className="flex h-[calc(100vh-64px)]">
			<div className="w-80 bg-white border-r border-gray-200 flex flex-col p-2">
				{/* <div className="flex-1 flex flex-col"> */}
				<Tabs
					className={'rp-[.ant-tabs-content-holder]:flex rp-[.ant-tabs-content-top]:flex flex-1'}
					activeKey={activeTree}
					centered
					onChange={(key: string) => setActiveTree(key)}
					items={[
						{
							key: 'data',
							label: '数据',
							children: (
								<Flex vertical gap={'small'} className={'flex-1 h-full'}>
									<Input
										placeholder="请输入搜索内容"
										prefix={<SearchOutlined />}
										value={dataSearchValue}
										onChange={(e) => setDataSearchValue(e.target.value)}
										allowClear
									/>
									<div className="flex overflow-y-auto h-[calc(100vh-210px)]">
										<Collapse
											size="small"
											ghost
											className="flex-1"
											items={collapseItems}
										/>
									</div>
									<Button type='text' icon={<PlusOutlined />} onClick={handleAddFolder} className="w-full">
										新建文件夹
									</Button>
								</Flex>
							),
							className: 'flex flex-1',
						},
						{
							key: 'view',
							label: '视图',
							children: (
								<Flex vertical gap={'small'} className={'flex-1 h-full'}>
									<Input
										placeholder="请输入搜索内容"
										prefix={<SearchOutlined />}
										value={viewSearchValue}
										onChange={(e) => setViewSearchValue(e.target.value)}
										allowClear
									/>
									<div className="flex overflow-y-auto h-[calc(100vh-210px)]">
										{filteredViewTreeData.length > 0 ? (
											<Tree
												treeData={filteredViewTreeData}
												expandedKeys={viewExpandedKeys}
												selectedKeys={viewSelectedKeys}
												onSelect={handleViewSelect}
												onExpand={setViewExpandedKeys}
												showLine={{ showLeafIcon: false }}
												titleRender={(nodeData) => renderCustomViewTitle(nodeData)}
												blockNode
											/>
										) : (
											<Empty description={viewSearchValue ? '未找到匹配的数据' : '暂无数据'} />
										)}
									</div>
									<Button type='text' icon={<PlusOutlined />} onClick={handleAddViewFolder} className="w-full">
										新建文件夹
									</Button>
								</Flex>
							),
							className: 'flex flex-1',
						}
					]}
				/>
				{/* </div> */}
			</div>
			<div className="flex-1 mx-2 flex flex-col">
				<div className="flex justify-between items-center my-2">
					<Space>
						<Button onClick={() => {
							navigate('/publicWorkspace/addView')
						}}>
							新建视图
						</Button>
						{
							activeTree === 'view' &&
							<Button>
								编辑视图
							</Button>
						}
					</Space>
					{
						activeTree === 'data' &&
						<Button onClick={() => {
							setEditDbInitialValues({
								name: '数据库名称',
								type: 'mysql',
								host: '主机',
								port: 0,
								username: '用户名',
								password: '密码',
							});
							setEditDbModalOpen(true);
						}}>
							编辑表
						</Button>
					}
				</div>
				<Tabs
					activeKey={activeTab}
					// centered
					className={'rp-[.ant-tabs-content-holder]:flex rp-[.ant-tabs-content-top]:flex flex-1'}
					onChange={handleTabChange}
					items={[
						{
							key: 'dataPreview',
							label: '数据预览',
							children: <WorkspaceDataPreview entityId={selectedEntityId} />,
							className: 'flex flex-1'
						},
						{
							key: 'relatedView',
							label: '关联视图',
							children: <RelationView />,
							className: 'flex flex-1'
						}
					]}
				/>
			</div>
			<DatabaseTableCreateModal
				open={createDbModalOpen}
				onOk={() => {
					handleAddDataTable(addKey);
					setCreateDbModalOpen(false);
					message.success('创建成功');
				}}
				onCancel={() => {
					setAddKey('')
					setCreateDbModalOpen(false)
				}}
				onTestConnection={() => message.success('连接成功')}
			/>
			<DatabaseTableEditModal
				open={editDbModalOpen}
				initialValues={editDbInitialValues}
				onOk={() => { setEditDbModalOpen(false); message.success('编辑成功'); }}
				onCancel={() => setEditDbModalOpen(false)}
				onTestConnection={() => message.success('连接成功')}
			/>
		</div>
	);
};

export default PublicWorkSpace;
