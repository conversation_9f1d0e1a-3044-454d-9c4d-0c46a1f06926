import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON> } from 'antd';
import DetailConfigStep from '../components/DetailConfigStep';
import type { DetailConfig } from '../types/component';

const DetailConfigTest: React.FC = () => {
  const [detailConfig, setDetailConfig] = useState<DetailConfig | undefined>(undefined);

  // 模拟选中的数据源和参数
  const mockSelectedDataSource = 'ds-001';
  const mockRelatedDataSources = ['ds-002'];
  const mockSelectedParameters = [
    {
      id: 'param-1',
      name: '用户ID',
      type: 'string',
      description: '用户唯一标识符',
      defaultValue: 'user123',
      required: true,
      dataSourceId: 'ds-001'
    },
    {
      id: 'param-2',
      name: '显示模式',
      type: 'string',
      description: '详情页面显示模式',
      defaultValue: 'full',
      required: false,
      dataSourceId: 'ds-001'
    }
  ];

  const handleConfigChange = (config: DetailConfig) => {
    setDetailConfig(config);
    console.log('详情配置更新:', config);
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <Card title="详情组件配置测试" className="mb-6">
        <div className="mb-4">
          <h3 className="text-lg font-medium mb-2">测试说明</h3>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• 左侧显示可用的数据源字段</li>
            <li>• 拖拽字段到中间区域添加到详情页面</li>
            <li>• 可以调整字段顺序、可见性和格式</li>
            <li>• 右侧显示组件参数信息</li>
            <li>• 实时预览详情页面效果</li>
          </ul>
        </div>
        
        <DetailConfigStep
          selectedDataSource={mockSelectedDataSource}
          relatedDataSources={mockRelatedDataSources}
          selectedParameters={mockSelectedParameters}
          value={detailConfig}
          onChange={handleConfigChange}
        />
      </Card>

      {/* 配置结果展示 */}
      {detailConfig && (
        <Card title="当前配置结果" className="mt-6">
          <pre className="bg-gray-50 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(detailConfig, null, 2)}
          </pre>
        </Card>
      )}
    </div>
  );
};

export default DetailConfigTest;
