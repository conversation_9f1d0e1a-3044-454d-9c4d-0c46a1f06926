import { useState } from 'react';
import { Tabs, Flex } from 'antd';
import { AppstoreOutlined, SettingOutlined } from '@ant-design/icons';
import type { ComponentItem } from '../types/component';

// 导入子组件
import ComponentList from '../components/ComponentList';
import ComponentPreview from '../components/ComponentPreview';
import CreateComponentModal from '../components/CreateComponentModal';
// import PanelTab from '../components/PanelTab';

const PanelTab = () => (
  <div className="p-4 text-gray-500">
    面板Tab内容 - 待实现
  </div>
);

const PageBuilder = () => {
  // 状态管理
  const [activeTab, setActiveTab] = useState<string>('components');
  const [selectedComponent, setSelectedComponent] = useState<ComponentItem | undefined>();
  const [createModalOpen, setCreateModalOpen] = useState<boolean>(false);

  // Tab切换处理
  const handleTabChange = (key: string) => {
    setActiveTab(key);
    // 切换Tab时清空选中的组件
    if (key !== 'components') {
      setSelectedComponent(undefined);
    }
  };

  // 组件选择处理
  const handleComponentSelect = (component: ComponentItem) => {
    setSelectedComponent(component);
  };

  // 新建组件处理
  const handleCreateComponent = () => {
    setCreateModalOpen(true);
  };

  // 编辑组件处理
  const handleEditComponent = (component: ComponentItem) => {
    // TODO: 打开编辑组件弹窗
    console.log('编辑组件:', component.name);
  };

  // 创建组件成功处理
  const handleCreateSuccess = (component: ComponentItem) => {
    // TODO: 将新组件添加到组件列表
    console.log('创建组件成功:', component);
    setCreateModalOpen(false);
    setSelectedComponent(component);
  };

  // 取消创建组件
  const handleCreateCancel = () => {
    setCreateModalOpen(false);
  };

  return (
    <div className="flex h-[calc(100vh-64px)]">
      {/* 左侧面板 */}
      <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
        <Tabs
          activeKey={activeTab}
          className="rp-[.ant-tabs-content-holder]:flex rp-[.ant-tabs-content-top]:flex flex-1"
          onChange={handleTabChange}
          items={[
            {
              key: 'components',
              label: (
                <span>
                  <AppstoreOutlined />
                  组件
                </span>
              ),
              children: (
                <Flex vertical className="flex-1 h-full">
                  <ComponentList
                    onComponentSelect={handleComponentSelect}
                    onCreateComponent={handleCreateComponent}
                  />
                </Flex>
              ),
              className: 'flex flex-1'
            },
            {
              key: 'panels',
              label: (
                <span>
                  <SettingOutlined />
                  面板
                </span>
              ),
              children: (
                <Flex vertical className="flex-1 h-full">
                  <PanelTab />
                </Flex>
              ),
              className: 'flex flex-1'
            }
          ]}
        />
      </div>

      {/* 右侧内容区 */}
      <div className="flex-1 bg-gray-50 flex flex-col">
        {activeTab === 'components' ? (
          // 组件Tab的右侧内容
          <div className="flex-1 flex flex-col">
            <div className="bg-white border-b border-gray-200 p-4">
              <h2 className="text-lg font-medium text-gray-900">
                {selectedComponent ? '组件预览' : '组件管理'}
              </h2>
              <p className="text-sm text-gray-500 mt-1">
                {selectedComponent
                  ? `预览组件: ${selectedComponent.name}`
                  : '请从左侧选择一个组件进行预览和配置'
                }
              </p>
            </div>
            <div className="flex-1 overflow-auto">
              <ComponentPreview
                component={selectedComponent}
                onEdit={handleEditComponent}
              />
            </div>
          </div>
        ) : (
          // 面板Tab的右侧内容
          <div className="flex-1 flex flex-col">
            <div className="bg-white border-b border-gray-200 p-4">
              <h2 className="text-lg font-medium text-gray-900">面板配置</h2>
              <p className="text-sm text-gray-500 mt-1">
                配置页面面板的布局和属性
              </p>
            </div>
            <div className="flex-1 overflow-auto p-4">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="text-center text-gray-500">
                  面板配置功能开发中...
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 新建组件弹窗 */}
      <CreateComponentModal
        open={createModalOpen}
        onOk={handleCreateSuccess}
        onCancel={handleCreateCancel}
      />
    </div>
  );
};

export default PageBuilder;
