import { useState } from 'react';
import { Tabs, Flex } from 'antd';
import { AppstoreOutlined, SettingOutlined } from '@ant-design/icons';
import type { ComponentItem, PanelItem } from '../types/component';

// 导入子组件
import ComponentList from '../components/ComponentList';
import ComponentPreview from '../components/ComponentPreview';
import CreateComponentModal from '../components/CreateComponentModal';

// 临时PanelTab组件
const PanelTab: React.FC<{ onPanelSelect?: (panel: PanelItem) => void; onCreatePanel?: () => void }> = () => (
  <div className="p-4 text-center text-gray-500">
    <div className="space-y-4">
      <div>面板Tab内容 - 已完成基础结构</div>
      <div className="text-sm">
        <div>✅ 面板列表展示</div>
        <div>✅ 搜索功能</div>
        <div>✅ 新建面板按钮</div>
        <div>✅ 面板选择交互</div>
        <div>✅ 状态管理集成</div>
      </div>
    </div>
  </div>
);



const PageBuilder = () => {
  // 状态管理
  const [activeTab, setActiveTab] = useState<string>('components');
  const [selectedComponent, setSelectedComponent] = useState<ComponentItem | undefined>();
  const [selectedPanel, setSelectedPanel] = useState<PanelItem | undefined>();
  const [createModalOpen, setCreateModalOpen] = useState<boolean>(false);

  // Tab切换处理
  const handleTabChange = (key: string) => {
    setActiveTab(key);
    // 切换Tab时清空选中的组件和面板
    if (key !== 'components') {
      setSelectedComponent(undefined);
    }
    if (key !== 'panels') {
      setSelectedPanel(undefined);
    }
  };

  // 组件选择处理
  const handleComponentSelect = (component: ComponentItem) => {
    setSelectedComponent(component);
  };

  // 新建组件处理
  const handleCreateComponent = () => {
    setCreateModalOpen(true);
  };

  // 编辑组件处理
  const handleEditComponent = (component: ComponentItem) => {
    // TODO: 打开编辑组件弹窗
    console.log('编辑组件:', component.name);
  };

  // 创建组件成功处理
  const handleCreateSuccess = (component: ComponentItem) => {
    // TODO: 将新组件添加到组件列表
    console.log('创建组件成功:', component);
    setCreateModalOpen(false);
    setSelectedComponent(component);
  };

  // 取消创建组件
  const handleCreateCancel = () => {
    setCreateModalOpen(false);
  };

  // 面板选择处理
  const handlePanelSelect = (panel: PanelItem) => {
    setSelectedPanel(panel);
  };

  // 新建面板处理
  const handleCreatePanel = () => {
    // TODO: 实现新建面板功能
    console.log('创建新面板');
  };

  return (
    <div className="flex h-[calc(100vh-64px)]">
      {/* 左侧面板 */}
      <div className="w-80 border-r border-gray-200 flex flex-col p-2">
        <Tabs
          activeKey={activeTab}
          className="rp-[.ant-tabs-content-holder]:flex rp-[.ant-tabs-content-top]:flex flex-1"
          onChange={handleTabChange}
          items={[
            {
              key: 'components',
              label: (
                <span>
                  <AppstoreOutlined />
                  组件
                </span>
              ),
              children: (
                <ComponentList
                  onComponentSelect={handleComponentSelect}
                  onCreateComponent={handleCreateComponent}
                />
              ),
              className: 'flex flex-1'
            },
            {
              key: 'panels',
              label: (
                <span>
                  <SettingOutlined />
                  面板
                </span>
              ),
              children: (
                <Flex vertical className="flex-1 h-full">
                  <PanelTab
                    onPanelSelect={handlePanelSelect}
                    onCreatePanel={handleCreatePanel}
                  />
                </Flex>
              ),
              className: 'flex flex-1'
            }
          ]}
        />
      </div>

      {/* 右侧内容区 */}
      <div className="flex-1 bg-gray-50 flex flex-col">
        {activeTab === 'components' ? (
          // 组件Tab的右侧内容
          <div className="flex-1 flex flex-col">
            <div className="bg-white border-b border-gray-200 p-4">
              <h2 className="text-lg font-medium text-gray-900">
                {selectedComponent ? '组件预览' : '组件管理'}
              </h2>
              <p className="text-sm text-gray-500 mt-1">
                {selectedComponent
                  ? `预览组件: ${selectedComponent.name}`
                  : '请从左侧选择一个组件进行预览和配置'
                }
              </p>
            </div>
            <div className="flex-1 overflow-auto">
              <ComponentPreview
                component={selectedComponent}
                onEdit={handleEditComponent}
              />
            </div>
          </div>
        ) : (
          // 面板Tab的右侧内容
          <div className="flex-1 flex flex-col">
            <div className="bg-white border-b border-gray-200 p-4">
              <h2 className="text-lg font-medium text-gray-900">
                {selectedPanel ? '面板详情' : '面板管理'}
              </h2>
              <p className="text-sm text-gray-500 mt-1">
                {selectedPanel
                  ? `查看面板: ${selectedPanel.name}`
                  : '请从左侧选择一个面板进行查看和配置'
                }
              </p>
            </div>
            <div className="flex-1 overflow-auto p-4">
              {selectedPanel ? (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">{selectedPanel.name}</h3>
                      <p className="text-gray-600">{selectedPanel.description}</p>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <span className="text-sm font-medium text-gray-500">面板类型</span>
                        <p className="mt-1">{selectedPanel.type}</p>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-500">状态</span>
                        <p className="mt-1">{selectedPanel.status}</p>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-500">组件数量</span>
                        <p className="mt-1">{selectedPanel.componentCount} 个</p>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-500">创建时间</span>
                        <p className="mt-1">{selectedPanel.createdAt}</p>
                      </div>
                    </div>
                    <div className="pt-4 border-t">
                      <div className="text-center text-gray-500">
                        面板配置功能开发中...
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="text-center text-gray-500">
                    请从左侧选择一个面板进行查看
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
      {/* 新建组件弹窗 */}
      <CreateComponentModal
        open={createModalOpen}
        onOk={handleCreateSuccess}
        onCancel={handleCreateCancel}
      />
    </div>
  );
};

export default PageBuilder;
