import { Input, Select, Table,Typography } from "antd";
import { useMemo, useCallback } from "react";
import { TableInfo } from "../types";
import { useTableScroll } from "../hooks/useTableScroll";

// 表结构编辑器组件
interface TableStructureEditorProps {
    table: TableInfo;
    onFieldUpdate: (tableId: string, fieldIndex: number, field: string, value: any) => void;
}
const fieldTypeOptions = [
    { label: '文本', value: 'text' },
    { label: '日期', value: 'date' },
    { label: '数值-整数', value: 'integer' },
    { label: '数值-小数', value: 'decimal' },
    { label: '图片', value: 'image' },
    { label: '数组', value: 'array' },
    { label: '列表', value: 'list' },
];

const TableStructureEditor = ({ table, onFieldUpdate }: TableStructureEditorProps) => {
    // 缓存表格数据源
    const dataSource = useMemo(() => {
        return table.fields.map((field, index) => ({
            ...field,
            key: index
        }));
    }, [table.fields]);

    // 缓存字段名更新回调
    const handleFieldNameChange = useCallback((index: number, value: string) => {
        onFieldUpdate(table.id, index, 'name', value);
    }, [table.id, onFieldUpdate]);

    // 缓存字段类型更新回调
    const handleFieldTypeChange = useCallback((index: number, value: string) => {
        onFieldUpdate(table.id, index, 'type', value);
    }, [table.id, onFieldUpdate]);

    // 缓存字段描述更新回调
    const handleFieldDescriptionChange = useCallback((index: number, value: string) => {
        onFieldUpdate(table.id, index, 'description', value);
    }, [table.id, onFieldUpdate]);

    // 缓存表格列定义
    const columns = useMemo(() => [
        {
            title: '字段名',
            dataIndex: 'name',
            key: 'name',
            width: 80,
            ellipsis: true,
            render: (text: string, _: any, index: number) => (
                <Input
                    value={text}
                    onChange={(e) => handleFieldNameChange(index, e.target.value)}
                    placeholder="字段名"
                    size="small"
                />
            )
        },
        {
            title: '字段类型',
            dataIndex: 'type',
            key: 'type',
            width: 100,
            // ellipsis: true,
            render: (text: string, _: any, index: number) => (
                <Select
                    value={text}
                    onChange={(value) => handleFieldTypeChange(index, value)}
                    options={fieldTypeOptions}
                    className="w-full"
                    size="small"
                />
            )
        },
        {
            title: '字段说明',
            dataIndex: 'description',
            key: 'description',
            width: 80,
            render: (text: string, _: any, index: number) => (
                <Input
                    value={text}
                    onChange={(e) => handleFieldDescriptionChange(index, e.target.value)}
                    placeholder="字段说明"
                    size="small"
                />
            )
        },
        {
            title: '原始字段名',
            dataIndex: 'originalName',
            key: 'originalName',
            width: 80,
            ellipsis: true,
            render: (text: string) => (
                <span className="text-gray-500 text-xs">{text}</span>
            )
        }
    ], [handleFieldNameChange, handleFieldTypeChange, handleFieldDescriptionChange]);
    const { tableRef, scroll } = useTableScroll();
    return (
        <div className="min-w-0">
            <h3 className="mb-2">表结构</h3>
            <Table
                ref={tableRef}
                dataSource={dataSource}
                columns={columns}
                pagination={false}
                // size="small"
                scroll={{ ...scroll, x: '100%' }}
            />
        </div>
    );
};
export default TableStructureEditor;