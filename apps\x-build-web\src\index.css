@layer tailwind-base, antd;

@layer tailwind-base {
  @tailwind base;
}

@tailwind components;
@tailwind utilities;

/* @layer components {
  .ant-tabs-content-top {
    @apply flex flex-1 bg-red-400
  }
} */
.react-flow__panel.react-flow__attribution {
  display: none !important;
}

body,
html {
  padding: 0;
  margin: 0;
}

/* 自定义动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

.animate-slideInRight {
  animation: slideInRight 0.3s ease-out;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* 自定义步骤样式 */
.custom-steps .ant-steps-item-process .ant-steps-item-icon {
  background: linear-gradient(135deg, #3b82f6, #6366f1);
  border-color: #3b82f6;
}

.custom-steps .ant-steps-item-finish .ant-steps-item-icon {
  background: linear-gradient(135deg, #10b981, #059669);
  border-color: #10b981;
}

/* 创建组件Modal样式 */
.create-component-modal .ant-modal-content {
  border-radius: 12px;
  overflow: hidden;
  max-height: calc(100vh - 40px);
}

.create-component-modal .ant-modal-header {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-bottom: 1px solid #e2e8f0;
}

.create-component-modal .ant-modal-body {
  padding-right: 6px;
  scroll-behavior: smooth;
}

/* 自定义滚动条样式 */
.create-component-modal .ant-modal-body::-webkit-scrollbar {
  width: 6px;
}

.create-component-modal .ant-modal-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.create-component-modal .ant-modal-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  transition: background 0.2s ease;
}

.create-component-modal .ant-modal-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式高度调整 */
@media (max-height: 800px) {
  .create-component-modal .ant-modal-content {
    max-height: calc(100vh - 20px);
  }

  .create-component-modal .ant-modal-body {
    max-height: calc(100vh - 160px);
  }
}

@media (max-height: 600px) {
  .create-component-modal .ant-modal-content {
    max-height: calc(100vh - 10px);
  }

  .create-component-modal .ant-modal-body {
    max-height: calc(100vh - 120px);
  }
}

/* 移动设备优化 */
@media (max-width: 768px) {
  .create-component-modal .ant-modal-content {
    margin: 10px;
    max-height: calc(100vh - 20px);
  }

  .create-component-modal .ant-modal-body {
    max-height: calc(100vh - 140px);
    padding: 16px 12px;
  }
}

/* 滚动条在移动设备上的优化 */
@media (max-width: 768px) {
  .create-component-modal .ant-modal-body::-webkit-scrollbar {
    width: 4px;
  }
}

/* 滚动指示器 */
.create-component-modal .ant-modal-footer {
  position: relative;
}

.create-component-modal .ant-modal-footer::before {
  content: '';
  position: absolute;
  top: -10px;
  left: 0;
  right: 0;
  height: 10px;
  background: linear-gradient(to top, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
  pointer-events: none;
}

/* 拖拽区域样式优化 */
.drag-over {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe) !important;
  border: 2px dashed #3b82f6 !important;
  transform: scale(1.02);
  transition: all 0.2s ease-in-out;
}

.drag-item {
  transition: all 0.2s ease-in-out;
}

.drag-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}