@layer tailwind-base, antd;

@layer tailwind-base {
  @tailwind base;
}

@tailwind components;
@tailwind utilities;

/* @layer components {
  .ant-tabs-content-top {
    @apply flex flex-1 bg-red-400
  }
} */
.react-flow__panel.react-flow__attribution {
  display: none !important;
}

body,
html {
  padding: 0;
  margin: 0;
}

/* 自定义动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

.animate-slideInRight {
  animation: slideInRight 0.3s ease-out;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* 自定义步骤样式 */
.custom-steps .ant-steps-item-process .ant-steps-item-icon {
  background: linear-gradient(135deg, #3b82f6, #6366f1);
  border-color: #3b82f6;
}

.custom-steps .ant-steps-item-finish .ant-steps-item-icon {
  background: linear-gradient(135deg, #10b981, #059669);
  border-color: #10b981;
}



/* 拖拽区域样式优化 */
.drag-over {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe) !important;
  border: 2px dashed #3b82f6 !important;
  transform: scale(1.02);
  transition: all 0.2s ease-in-out;
}

.drag-item {
  transition: all 0.2s ease-in-out;
}

.drag-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}