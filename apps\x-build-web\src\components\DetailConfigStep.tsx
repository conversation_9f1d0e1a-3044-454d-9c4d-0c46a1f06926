import React, { useState, useEffect } from 'react';
import { Card, Checkbox, Button, Input, Select, Form, Tag, Empty, Tree } from 'antd';
import { DndContext, DragEndEvent, DragStartEvent, useDraggable, useDroppable, DragOverlay } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy, useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { 
  FieldStringOutlined, 
  FieldNumberOutlined, 
  FieldTimeOutlined, 
  FieldBinaryOutlined,
  HolderOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  EditOutlined
} from '@ant-design/icons';
import type { DetailConfig, DetailField, DataSourceInfo, DataSourceField } from '../types/component';
import { mockDataSources } from '../mock/componentData';

interface DetailConfigStepProps {
  selectedDataSource: string;
  relatedDataSources: string[];
  selectedParameters: any[];
  value?: DetailConfig;
  onChange?: (config: DetailConfig) => void;
}

// 字段类型图标映射
const fieldTypeIcons: Record<string, React.ReactNode> = {
  string: <FieldStringOutlined className="text-blue-500" />,
  number: <FieldNumberOutlined className="text-green-500" />,
  date: <FieldTimeOutlined className="text-orange-500" />,
  boolean: <FieldBinaryOutlined className="text-purple-500" />,
  array: <FieldStringOutlined className="text-gray-500" />
};

// 可拖拽字段组件
const DraggableField: React.FC<{ field: DataSourceField; dataSourceId: string }> = ({ field, dataSourceId }) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: `${dataSourceId}-${field.name}`,
    data: { field, dataSourceId }
  });

  const style = {
    transform: CSS.Translate.toString(transform),
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...listeners}
      {...attributes}
      className="flex items-center gap-2 p-2 bg-white border rounded cursor-move hover:bg-blue-50 hover:border-blue-300 transition-all"
    >
      {fieldTypeIcons[field.type]}
      <span className="text-sm">{field.name}</span>
      {field.description && (
        <span className="text-xs text-gray-500">({field.description})</span>
      )}
    </div>
  );
};

// 可排序的详情字段组件
const SortableDetailField: React.FC<{
  field: DetailField;
  onToggleVisible: (id: string) => void;
  onEdit: (id: string) => void;
  onRemove: (id: string) => void;
}> = ({ field, onToggleVisible, onEdit, onRemove }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: field.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`flex items-center gap-2 p-3 border rounded ${
        field.visible ? 'bg-white border-blue-200' : 'bg-gray-50 border-gray-200'
      }`}
    >
      <div {...attributes} {...listeners} className="cursor-move text-gray-400 hover:text-gray-600">
        <HolderOutlined />
      </div>
      
      <div className="flex-1">
        <div className="flex items-center gap-2">
          <span className={`font-medium ${field.visible ? 'text-gray-900' : 'text-gray-500'}`}>
            {field.displayName || field.name}
          </span>
          <Tag size="small" color="blue">{field.fieldName}</Tag>
        </div>
        {(field.prefix || field.suffix || field.format) && (
          <div className="text-xs text-gray-500 mt-1">
            {field.prefix && `前缀: ${field.prefix} `}
            {field.format && `格式: ${field.format} `}
            {field.suffix && `后缀: ${field.suffix}`}
          </div>
        )}
      </div>

      <div className="flex items-center gap-1">
        <Button
          type="text"
          size="small"
          icon={field.visible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
          onClick={() => onToggleVisible(field.id)}
          className={field.visible ? 'text-blue-500' : 'text-gray-400'}
        />
        <Button
          type="text"
          size="small"
          icon={<EditOutlined />}
          onClick={() => onEdit(field.id)}
          className="text-gray-500 hover:text-blue-500"
        />
        <Button
          type="text"
          size="small"
          onClick={() => onRemove(field.id)}
          className="text-gray-500 hover:text-red-500"
        >
          ×
        </Button>
      </div>
    </div>
  );
};

// 拖拽放置区域
const DroppableArea: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const { setNodeRef } = useDroppable({
    id: 'detail-fields',
  });

  return (
    <div
      ref={setNodeRef}
      className="min-h-[200px] p-4 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50"
    >
      {children}
    </div>
  );
};

const DetailConfigStep: React.FC<DetailConfigStepProps> = ({
  selectedDataSource,
  relatedDataSources,
  selectedParameters,
  value,
  onChange
}) => {
  const [form] = Form.useForm();
  const [detailFields, setDetailFields] = useState<DetailField[]>(value?.fields || []);
  const [activeId, setActiveId] = useState<string | null>(null);
  const [editingField, setEditingField] = useState<DetailField | null>(null);
  const [editForm] = Form.useForm();

  // 获取所有可用字段
  const getAllFields = (): { dataSource: DataSourceInfo; fields: DataSourceField[] }[] => {
    const allDataSources = [selectedDataSource, ...relatedDataSources];
    return allDataSources
      .map(dsId => {
        const ds = mockDataSources.find(d => d.id === dsId);
        return ds ? { dataSource: ds, fields: ds.fields || [] } : null;
      })
      .filter(Boolean) as { dataSource: DataSourceInfo; fields: DataSourceField[] }[];
  };

  // 构建字段树数据
  const fieldsTreeData = getAllFields().map(({ dataSource, fields }) => ({
    title: (
      <div className="flex items-center gap-2">
        <Tag color={dataSource.id === selectedDataSource ? 'blue' : 'green'}>
          {dataSource.type || 'table'}
        </Tag>
        <span className="font-medium">{dataSource.name}</span>
        <span className="text-xs text-gray-500">({fields.length} 字段)</span>
      </div>
    ),
    key: dataSource.id,
    children: fields.map(field => ({
      title: (
        <DraggableField
          key={`${dataSource.id}-${field.name}`}
          field={field}
          dataSourceId={dataSource.id}
        />
      ),
      key: `${dataSource.id}-${field.name}`,
      isLeaf: true,
    })),
  }));

  // 处理拖拽结束
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveId(null);

    if (!over || over.id !== 'detail-fields') return;

    const dragData = active.data.current;
    if (!dragData) return;

    const { field, dataSourceId } = dragData;
    
    // 检查是否已存在
    const existingField = detailFields.find(f => 
      f.fieldName === field.name && f.dataSourceId === dataSourceId
    );
    
    if (existingField) return;

    const newField: DetailField = {
      id: `${dataSourceId}-${field.name}-${Date.now()}`,
      name: field.name,
      fieldName: field.name,
      dataSourceId,
      displayName: field.name,
      order: detailFields.length,
      visible: true,
    };

    setDetailFields(prev => [...prev, newField]);
  };

  // 切换字段可见性
  const handleToggleVisible = (fieldId: string) => {
    setDetailFields(prev =>
      prev.map(field =>
        field.id === fieldId ? { ...field, visible: !field.visible } : field
      )
    );
  };

  // 编辑字段
  const handleEditField = (fieldId: string) => {
    const field = detailFields.find(f => f.id === fieldId);
    if (field) {
      setEditingField(field);
      editForm.setFieldsValue({
        displayName: field.displayName,
        prefix: field.prefix,
        suffix: field.suffix,
        format: field.format,
      });
    }
  };

  // 保存字段编辑
  const handleSaveEdit = () => {
    if (!editingField) return;
    
    const values = editForm.getFieldsValue();
    setDetailFields(prev =>
      prev.map(field =>
        field.id === editingField.id
          ? { ...field, ...values }
          : field
      )
    );
    setEditingField(null);
  };

  // 移除字段
  const handleRemoveField = (fieldId: string) => {
    setDetailFields(prev => prev.filter(field => field.id !== fieldId));
  };

  // 更新配置
  useEffect(() => {
    const formValues = form.getFieldsValue();
    const config: DetailConfig = {
      fields: detailFields,
      title: formValues.title || '详情页面',
      layout: formValues.layout || 'vertical',
      fieldSpacing: formValues.fieldSpacing || 16,
      showFieldLabels: formValues.showFieldLabels ?? true,
      labelWidth: formValues.labelWidth || 120,
    };
    onChange?.(config);
  }, [detailFields, form, onChange]);

  return (
    <div className="space-y-4">
      {/* 基础配置 */}
      <Card title="基础配置" size="small">
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            title: value?.title || '详情页面',
            layout: value?.layout || 'vertical',
            fieldSpacing: value?.fieldSpacing || 16,
            showFieldLabels: value?.showFieldLabels ?? true,
            labelWidth: value?.labelWidth || 120,
          }}
          onValuesChange={() => {
            // 触发配置更新
            const formValues = form.getFieldsValue();
            const config: DetailConfig = {
              fields: detailFields,
              title: formValues.title || '详情页面',
              layout: formValues.layout || 'vertical',
              fieldSpacing: formValues.fieldSpacing || 16,
              showFieldLabels: formValues.showFieldLabels ?? true,
              labelWidth: formValues.labelWidth || 120,
            };
            onChange?.(config);
          }}
        >
          <div className="grid grid-cols-2 gap-4">
            <Form.Item label="详情标题" name="title">
              <Input placeholder="请输入详情标题" />
            </Form.Item>
            <Form.Item label="布局方式" name="layout">
              <Select>
                <Select.Option value="vertical">垂直布局</Select.Option>
                <Select.Option value="horizontal">水平布局</Select.Option>
                <Select.Option value="grid">网格布局</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item label="字段间距(px)" name="fieldSpacing">
              <Input type="number" min={0} max={50} />
            </Form.Item>
            <Form.Item label="标签宽度(px)" name="labelWidth">
              <Input type="number" min={80} max={200} />
            </Form.Item>
          </div>
          <Form.Item name="showFieldLabels" valuePropName="checked">
            <Checkbox>显示字段标签</Checkbox>
          </Form.Item>
        </Form>
      </Card>

      {/* 字段配置与预览 */}
      <div className="grid grid-cols-3 gap-6">
        {/* 左侧：字段选择 */}
        <div className="space-y-4">
          <Card title="字段选择" size="small">
            {fieldsTreeData.length > 0 ? (
              <Tree
                treeData={fieldsTreeData}
                showLine={{ showLeafIcon: false }}
                defaultExpandAll
                className="max-h-60 overflow-y-auto"
              />
            ) : (
              <Empty description="暂无数据源字段" />
            )}
            <div className="mt-2 text-xs text-gray-500">
              💡 拖拽字段到中间区域添加到详情页面
            </div>
          </Card>
        </div>

        {/* 中间：详情内容展示与调整 */}
        <div className="space-y-4">
          <Card title="详情内容展示与调整" size="small">
            <DndContext onDragEnd={handleDragEnd} onDragStart={(event) => setActiveId(event.active.id as string)}>
              <DroppableArea>
                {detailFields.length > 0 ? (
                  <SortableContext items={detailFields.map(f => f.id)} strategy={verticalListSortingStrategy}>
                    <div className="space-y-2">
                      {detailFields.map(field => (
                        <SortableDetailField
                          key={field.id}
                          field={field}
                          onToggleVisible={handleToggleVisible}
                          onEdit={handleEditField}
                          onRemove={handleRemoveField}
                        />
                      ))}
                    </div>
                  </SortableContext>
                ) : (
                  <div className="text-center text-gray-500 py-8">
                    <div className="text-4xl mb-2">📋</div>
                    <div>拖拽左侧字段到此处</div>
                    <div className="text-sm">可拖动调整位置，各字段间隔16px</div>
                  </div>
                )}
              </DroppableArea>

              <DragOverlay>
                {activeId ? (
                  <div className="p-2 bg-white border rounded shadow-lg">
                    拖拽中...
                  </div>
                ) : null}
              </DragOverlay>
            </DndContext>
          </Card>
        </div>

        {/* 右侧：组件参数 */}
        <div className="space-y-4">
          <Card title="组件参数" size="small">
            {selectedParameters.length > 0 ? (
              <div className="space-y-3">
                {selectedParameters.map(param => (
                  <div key={param.id} className="p-3 bg-gray-50 rounded border">
                    <div className="flex items-center gap-2 mb-2">
                      <Tag color="blue">{param.type}</Tag>
                      <span className="font-medium">{param.name}</span>
                    </div>
                    {param.description && (
                      <div className="text-sm text-gray-600 mb-2">
                        描述：{param.description}
                      </div>
                    )}
                    <div className="text-xs text-gray-500">
                      默认值：{param.defaultValue || '无'}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center text-gray-500 py-4">
                <div className="text-2xl mb-2">⚙️</div>
                <div>暂无组件参数</div>
                <div className="text-sm">在第一步中选择组件入参</div>
              </div>
            )}
          </Card>
        </div>
      </div>

      {/* 字段编辑弹窗 */}
      {editingField && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96">
            <h3 className="text-lg font-medium mb-4">编辑字段</h3>
            <Form form={editForm} layout="vertical">
              <Form.Item label="显示名称" name="displayName">
                <Input placeholder="请输入显示名称" />
              </Form.Item>
              <Form.Item label="前缀" name="prefix">
                <Input placeholder="请输入前缀" />
              </Form.Item>
              <Form.Item label="后缀" name="suffix">
                <Input placeholder="请输入后缀" />
              </Form.Item>
              <Form.Item label="格式化" name="format">
                <Input placeholder="请输入格式化规则" />
              </Form.Item>
            </Form>
            <div className="flex justify-end gap-2 mt-4">
              <Button onClick={() => setEditingField(null)}>取消</Button>
              <Button type="primary" onClick={handleSaveEdit}>确定</Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DetailConfigStep;
