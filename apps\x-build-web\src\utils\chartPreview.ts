import type { ChartType, ChartConfig, DimensionMetricField } from '../types/component';

// 生成模拟数据
export const generateMockData = (dimensions: DimensionMetricField[], metrics: DimensionMetricField[]) => {
  if (dimensions.length === 0 || metrics.length === 0) {
    return { categories: [], series: [] };
  }

  // 生成维度数据（类别）
  const categories = [
    '北京', '上海', '广州', '深圳', '杭州', '南京', '武汉', '成都'
  ].slice(0, 6);

  // 生成指标数据（数值）
  const series = metrics.map(metric => ({
    name: metric.name,
    data: categories.map(() => Math.floor(Math.random() * 1000) + 100)
  }));

  return { categories, series };
};

// 生成ECharts配置
export const generateChartOptions = (chartType: ChartType, config: ChartConfig): any => {
  const { dimensions, metrics, title, showLegend = true } = config;
  
  if (dimensions.length === 0 || metrics.length === 0) {
    return null;
  }

  const { categories, series } = generateMockData(dimensions, metrics);
  
  // 基础配置
  const baseOptions = {
    title: {
      text: title || '图表预览',
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      show: showLegend,
      top: 30,
      data: series.map(s => s.name)
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: showLegend ? '15%' : '10%',
      containLabel: true
    },
    animation: false
  };

  // 根据图表类型生成特定配置
  switch (chartType) {
    case 'bar':
      return {
        ...baseOptions,
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: { fontSize: 12 }
        },
        yAxis: {
          type: 'value',
          axisLabel: { fontSize: 12 }
        },
        series: series.map(s => ({
          ...s,
          type: 'bar',
          barWidth: '60%'
        }))
      };

    case 'line':
      return {
        ...baseOptions,
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: { fontSize: 12 }
        },
        yAxis: {
          type: 'value',
          axisLabel: { fontSize: 12 }
        },
        series: series.map(s => ({
          ...s,
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6
        }))
      };

    case 'area':
      return {
        ...baseOptions,
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: { fontSize: 12 }
        },
        yAxis: {
          type: 'value',
          axisLabel: { fontSize: 12 }
        },
        series: series.map(s => ({
          ...s,
          type: 'line',
          smooth: true,
          areaStyle: {},
          symbol: 'circle',
          symbolSize: 4
        }))
      };

    case 'pie':
    case 'doughnut':
      return {
        ...baseOptions,
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [{
          name: series[0]?.name || '数据',
          type: 'pie',
          radius: chartType === 'doughnut' ? ['40%', '70%'] : '70%',
          center: ['50%', '60%'],
          data: categories.map((cat, index) => ({
            name: cat,
            value: series[0]?.data[index] || 0
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      };

    case 'scatter':
      if (metrics.length < 2) {
        return null;
      }
      return {
        ...baseOptions,
        xAxis: {
          type: 'value',
          name: metrics[0].name,
          axisLabel: { fontSize: 12 }
        },
        yAxis: {
          type: 'value',
          name: metrics[1].name,
          axisLabel: { fontSize: 12 }
        },
        series: [{
          type: 'scatter',
          data: categories.map((_, index) => [
            series[0]?.data[index] || 0,
            series[1]?.data[index] || 0
          ]),
          symbolSize: 8
        }]
      };

    case 'radar':
      return {
        ...baseOptions,
        radar: {
          indicator: categories.map(cat => ({
            name: cat,
            max: Math.max(...series.flatMap(s => s.data))
          })),
          radius: '70%',
          center: ['50%', '60%']
        },
        series: [{
          type: 'radar',
          data: series.map(s => ({
            name: s.name,
            value: s.data
          }))
        }]
      };

    case 'funnel':
      return {
        ...baseOptions,
        series: [{
          type: 'funnel',
          left: '10%',
          top: '15%',
          width: '80%',
          height: '70%',
          data: categories.map((cat, index) => ({
            name: cat,
            value: series[0]?.data[index] || 0
          })).sort((a, b) => b.value - a.value)
        }]
      };

    case 'histogram':
      return {
        ...baseOptions,
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: { fontSize: 12 }
        },
        yAxis: {
          type: 'value',
          axisLabel: { fontSize: 12 }
        },
        series: [{
          type: 'bar',
          data: series[0]?.data || [],
          barWidth: '80%',
          itemStyle: {
            color: '#5470c6'
          }
        }]
      };

    case 'combo':
      return {
        ...baseOptions,
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: { fontSize: 12 }
        },
        yAxis: [
          {
            type: 'value',
            axisLabel: { fontSize: 12 }
          },
          {
            type: 'value',
            axisLabel: { fontSize: 12 }
          }
        ],
        series: [
          {
            name: series[0]?.name || '柱状',
            type: 'bar',
            data: series[0]?.data || []
          },
          {
            name: series[1]?.name || '折线',
            type: 'line',
            yAxisIndex: 1,
            data: series[1]?.data || series[0]?.data || []
          }
        ]
      };

    case 'tree':
      return {
        ...baseOptions,
        series: [{
          type: 'tree',
          data: [{
            name: '根节点',
            children: categories.map((cat, index) => ({
              name: cat,
              value: series[0]?.data[index] || 0
            }))
          }],
          top: '15%',
          left: '7%',
          bottom: '7%',
          right: '20%',
          symbolSize: 7,
          label: {
            position: 'left',
            verticalAlign: 'middle',
            align: 'right',
            fontSize: 12
          },
          leaves: {
            label: {
              position: 'right',
              verticalAlign: 'middle',
              align: 'left'
            }
          }
        }]
      };

    case 'graph':
      return {
        ...baseOptions,
        series: [{
          type: 'graph',
          layout: 'force',
          data: categories.map((cat, index) => ({
            name: cat,
            value: series[0]?.data[index] || 0,
            symbolSize: Math.max(20, (series[0]?.data[index] || 0) / 20)
          })),
          links: categories.slice(0, -1).map((_, index) => ({
            source: index,
            target: index + 1
          })),
          roam: true,
          force: {
            repulsion: 100
          }
        }]
      };

    case 'wordcloud':
      return {
        ...baseOptions,
        series: [{
          type: 'wordCloud',
          gridSize: 2,
          sizeRange: [12, 50],
          rotationRange: [-90, 90],
          shape: 'pentagon',
          width: '100%',
          height: '80%',
          top: '15%',
          data: categories.map((cat, index) => ({
            name: cat,
            value: series[0]?.data[index] || 0
          }))
        }]
      };

    default:
      return baseOptions;
  }
};

// 验证图表配置是否完整
export const validateChartConfig = (config: ChartConfig): { isValid: boolean; message?: string } => {
  const { chartType, dimensions, metrics } = config;
  
  if (dimensions.length === 0) {
    return { isValid: false, message: '请至少选择一个维度字段' };
  }
  
  if (metrics.length === 0) {
    return { isValid: false, message: '请至少选择一个指标字段' };
  }

  // 特殊图表类型的验证
  if (chartType === 'scatter' && metrics.length < 2) {
    return { isValid: false, message: '散点图需要至少两个指标字段' };
  }

  return { isValid: true };
};
