import { nanoid } from 'nanoid';
import type {
  ComponentItem,
  ComponentParameter,
  DataSourceInfo,
  DataSourceField,
  ComponentType,
  ComponentCategory
} from '../types/component';

// 数据源字段Mock数据
const createMockFields = (fieldNames: string[]): DataSourceField[] => {
  return fieldNames.map(name => ({
    name,
    type: ['string', 'number', 'date', 'boolean'][Math.floor(Math.random() * 4)] as any,
    description: `${name}字段描述`,
    nullable: Math.random() > 0.5
  }));
};

// Mock数据源列表
export const mockDataSources: DataSourceInfo[] = [
  {
    id: 'ds-001',
    name: '用户信息表',
    description: '用户基础信息数据表',
    type: 'table',
    fields: createMockFields(['用户ID', '用户名', '邮箱', '注册时间', '最后登录时间', '用户状态'])
  },
  {
    id: 'ds-002',
    name: '订单数据表',
    description: '电商订单交易数据',
    type: 'table',
    fields: createMockFields(['订单ID', '用户ID', '商品名称', '订单金额', '订单状态', '创建时间', '支付时间'])
  },
  {
    id: 'ds-003',
    name: '商品信息表',
    description: '商品基础信息和库存数据',
    type: 'table',
    fields: createMockFields(['商品ID', '商品名称', '分类', '价格', '库存数量', '销售量', '上架时间'])
  },
  {
    id: 'ds-004',
    name: '销售统计视图',
    description: '销售数据统计分析视图',
    type: 'view',
    fields: createMockFields(['日期', '销售额', '订单数量', '客单价', '退款金额', '净收入'])
  },
  {
    id: 'ds-005',
    name: '用户行为日志',
    description: '用户行为追踪日志数据',
    type: 'table',
    fields: createMockFields(['日志ID', '用户ID', '行为类型', '页面路径', '时间戳', 'IP地址', '设备信息'])
  }
];

// 数据源关联关系映射
export const mockRelatedDataSources: Record<string, string[]> = {
  'ds-001': ['ds-002', 'ds-005'], // 用户信息表 -> 订单数据表、用户行为日志
  'ds-002': ['ds-001', 'ds-003'], // 订单数据表 -> 用户信息表、商品信息表
  'ds-003': ['ds-002', 'ds-004'], // 商品信息表 -> 订单数据表、销售统计视图
  'ds-004': ['ds-002', 'ds-003'], // 销售统计视图 -> 订单数据表、商品信息表
  'ds-005': ['ds-001']            // 用户行为日志 -> 用户信息表
};

// 创建Mock参数的辅助函数
const createMockParameters = (dataSourceId: string, count: number = 3): ComponentParameter[] => {
  const dataSource = mockDataSources.find(ds => ds.id === dataSourceId);
  const relatedSources = mockRelatedDataSources[dataSourceId] || [];
  
  const parameters: ComponentParameter[] = [];
  
  // 从主数据源创建参数
  if (dataSource) {
    const mainFields = dataSource.fields.slice(0, Math.min(count, dataSource.fields.length));
    mainFields.forEach(field => {
      parameters.push({
        id: nanoid(),
        name: field.name,
        type: field.type,
        required: Math.random() > 0.5,
        source: 'dataSource',
        sourceField: field.name,
        description: `来自${dataSource.name}的${field.name}字段`
      });
    });
  }
  
  // 从关联数据源创建参数
  relatedSources.slice(0, 1).forEach(relatedId => {
    const relatedSource = mockDataSources.find(ds => ds.id === relatedId);
    if (relatedSource && relatedSource.fields.length > 0) {
      const field = relatedSource.fields[0];
      parameters.push({
        id: nanoid(),
        name: `关联_${field.name}`,
        type: field.type,
        required: false,
        source: 'relatedDataSource',
        sourceField: field.name,
        description: `来自关联数据源${relatedSource.name}的${field.name}字段`
      });
    }
  });
  
  return parameters;
};

// Mock组件列表
export const mockComponents: ComponentItem[] = [
  // 图表组件
  {
    id: nanoid(),
    name: '用户增长趋势图',
    type: 'chart',
    dataSource: 'ds-001',
    dataSourceName: '用户信息表',
    relatedDataSources: ['ds-005'],
    relatedDataSourceNames: ['用户行为日志'],
    parameters: createMockParameters('ds-001'),
    description: '展示用户注册数量的时间趋势变化',
    createdAt: new Date(Date.now() - 86400000 * 7).toISOString(), // 7天前
    updatedAt: new Date(Date.now() - 86400000 * 2).toISOString(), // 2天前
    config: {
      chartType: 'line',
      xAxis: '注册时间',
      yAxis: '用户数量'
    }
  },
  {
    id: nanoid(),
    name: '销售额分布饼图',
    type: 'chart',
    dataSource: 'ds-004',
    dataSourceName: '销售统计视图',
    relatedDataSources: ['ds-002', 'ds-003'],
    relatedDataSourceNames: ['订单数据表', '商品信息表'],
    parameters: createMockParameters('ds-004'),
    description: '按商品分类展示销售额分布情况',
    createdAt: new Date(Date.now() - 86400000 * 5).toISOString(),
    updatedAt: new Date(Date.now() - 86400000 * 1).toISOString(),
    config: {
      chartType: 'pie',
      dimension: '分类',
      measure: '销售额'
    }
  },
  {
    id: nanoid(),
    name: '订单状态柱状图',
    type: 'chart',
    dataSource: 'ds-002',
    dataSourceName: '订单数据表',
    relatedDataSources: ['ds-001'],
    relatedDataSourceNames: ['用户信息表'],
    parameters: createMockParameters('ds-002'),
    description: '展示不同订单状态的数量分布',
    createdAt: new Date(Date.now() - 86400000 * 3).toISOString(),
    updatedAt: new Date(Date.now() - 86400000 * 1).toISOString(),
    config: {
      chartType: 'bar',
      xAxis: '订单状态',
      yAxis: '订单数量'
    }
  },
  
  // 表格组件
  {
    id: nanoid(),
    name: '用户信息列表',
    type: 'table',
    dataSource: 'ds-001',
    dataSourceName: '用户信息表',
    relatedDataSources: ['ds-002'],
    relatedDataSourceNames: ['订单数据表'],
    parameters: createMockParameters('ds-001', 4),
    description: '展示用户基础信息的详细列表',
    createdAt: new Date(Date.now() - 86400000 * 6).toISOString(),
    updatedAt: new Date(Date.now() - 86400000 * 3).toISOString(),
    config: {
      pageSize: 20,
      sortable: true,
      filterable: true
    }
  },
  {
    id: nanoid(),
    name: '订单明细表',
    type: 'table',
    dataSource: 'ds-002',
    dataSourceName: '订单数据表',
    relatedDataSources: ['ds-001', 'ds-003'],
    relatedDataSourceNames: ['用户信息表', '商品信息表'],
    parameters: createMockParameters('ds-002', 5),
    description: '订单交易的详细信息表格',
    createdAt: new Date(Date.now() - 86400000 * 4).toISOString(),
    updatedAt: new Date(Date.now() - 86400000 * 2).toISOString(),
    config: {
      pageSize: 50,
      exportable: true,
      searchable: true
    }
  },
  
  // 卡片组件
  {
    id: nanoid(),
    name: '销售概览卡片',
    type: 'card',
    dataSource: 'ds-004',
    dataSourceName: '销售统计视图',
    relatedDataSources: ['ds-002'],
    relatedDataSourceNames: ['订单数据表'],
    parameters: createMockParameters('ds-004', 2),
    description: '展示关键销售指标的概览卡片',
    createdAt: new Date(Date.now() - 86400000 * 8).toISOString(),
    updatedAt: new Date(Date.now() - 86400000 * 1).toISOString(),
    config: {
      layout: 'horizontal',
      showTrend: true,
      refreshInterval: 300
    }
  },
  {
    id: nanoid(),
    name: '用户统计卡片',
    type: 'card',
    dataSource: 'ds-001',
    dataSourceName: '用户信息表',
    relatedDataSources: ['ds-005'],
    relatedDataSourceNames: ['用户行为日志'],
    parameters: createMockParameters('ds-001', 3),
    description: '用户相关统计数据的卡片展示',
    createdAt: new Date(Date.now() - 86400000 * 9).toISOString(),
    updatedAt: new Date(Date.now() - 86400000 * 4).toISOString(),
    config: {
      layout: 'vertical',
      showIcon: true,
      colorTheme: 'blue'
    }
  },
  
  // 详情组件
  {
    id: nanoid(),
    name: '商品详情页',
    type: 'detail',
    dataSource: 'ds-003',
    dataSourceName: '商品信息表',
    relatedDataSources: ['ds-002', 'ds-004'],
    relatedDataSourceNames: ['订单数据表', '销售统计视图'],
    parameters: createMockParameters('ds-003', 4),
    description: '商品的详细信息展示页面',
    createdAt: new Date(Date.now() - 86400000 * 10).toISOString(),
    updatedAt: new Date(Date.now() - 86400000 * 5).toISOString(),
    config: {
      layout: 'tabs',
      showRelated: true,
      allowEdit: false
    }
  },
  {
    id: nanoid(),
    name: '用户详情页',
    type: 'detail',
    dataSource: 'ds-001',
    dataSourceName: '用户信息表',
    relatedDataSources: ['ds-002', 'ds-005'],
    relatedDataSourceNames: ['订单数据表', '用户行为日志'],
    parameters: createMockParameters('ds-001', 6),
    description: '用户的详细信息和相关数据展示',
    createdAt: new Date(Date.now() - 86400000 * 12).toISOString(),
    updatedAt: new Date(Date.now() - 86400000 * 6).toISOString(),
    config: {
      layout: 'sections',
      showTimeline: true,
      allowEdit: true
    }
  }
];

// 组件分类信息
export const mockComponentCategories: ComponentCategory[] = [
  {
    type: 'chart',
    label: '图表组件',
    icon: 'BarChartOutlined',
    description: '用于数据可视化的各类图表组件'
  },
  {
    type: 'table',
    label: '表格组件',
    icon: 'TableOutlined',
    description: '用于展示结构化数据的表格组件'
  },
  {
    type: 'card',
    label: '卡片组件',
    icon: 'IdcardOutlined',
    description: '用于展示关键指标和摘要信息的卡片组件'
  },
  {
    type: 'detail',
    label: '详情组件',
    icon: 'ProfileOutlined',
    description: '用于展示详细信息的页面组件'
  }
];

// 可选参数列表（用于参数选择弹窗）
export const mockAvailableParameters: ComponentParameter[] = [
  {
    id: nanoid(),
    name: '时间范围',
    type: 'date',
    required: false,
    source: 'dataSource',
    description: '数据查询的时间范围筛选'
  },
  {
    id: nanoid(),
    name: '用户类型',
    type: 'string',
    required: false,
    source: 'dataSource',
    description: '用户类型筛选条件'
  },
  {
    id: nanoid(),
    name: '排序方式',
    type: 'string',
    required: false,
    source: 'dataSource',
    description: '数据排序方式设置'
  },
  {
    id: nanoid(),
    name: '显示数量',
    type: 'number',
    required: false,
    source: 'dataSource',
    description: '每页显示的数据条数',
    defaultValue: 20
  }
];

// 根据数据源ID获取关联数据源
export const getRelatedDataSources = (dataSourceId: string): DataSourceInfo[] => {
  const relatedIds = mockRelatedDataSources[dataSourceId] || [];
  return mockDataSources.filter(ds => relatedIds.includes(ds.id));
};

// 根据数据源ID获取可用参数
export const getAvailableParameters = (dataSourceId: string, relatedDataSourceIds: string[] = []): ComponentParameter[] => {
  const allDataSourceIds = [dataSourceId, ...relatedDataSourceIds];
  const parameters: ComponentParameter[] = [];
  
  allDataSourceIds.forEach(dsId => {
    const dataSource = mockDataSources.find(ds => ds.id === dsId);
    if (dataSource) {
      dataSource.fields.forEach(field => {
        parameters.push({
          id: nanoid(),
          name: field.name,
          type: field.type,
          required: false,
          source: dsId === dataSourceId ? 'dataSource' : 'relatedDataSource',
          sourceField: field.name,
          description: `来自${dataSource.name}的${field.name}字段`
        });
      });
    }
  });
  
  return [...parameters, ...mockAvailableParameters];
};
