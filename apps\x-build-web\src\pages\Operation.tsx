import { useState, useMemo } from 'react';
import {
	Button,
	Input,
	Tree,
	Modal,
	Form,
	Select,
	Dropdown,
	message,
	Flex
} from 'antd';
import {
	PlusOutlined,
	SearchOutlined,
	FolderOutlined,
	FileOutlined,
	MoreOutlined,
	DeleteOutlined,
	EditOutlined,
	CopyOutlined,
	ExclamationCircleOutlined
} from '@ant-design/icons';
import { nanoid } from 'nanoid';

// 数据类型定义
interface PageItem {
	id: string;
	name: string;
	type: 'page';
	folderId?: string;
	createdAt: Date;
	updatedAt: Date;
}

interface FolderItem {
	id: string;
	name: string;
	type: 'folder';
	parentId?: string;
	createdAt: Date;
	updatedAt: Date;
}

type TreeNodeData = (PageItem | FolderItem) & {
	key: string;
	title: string;
	children?: TreeNodeData[];
};

// Mock数据
const mockFolders: FolderItem[] = [
	{
		id: 'folder1',
		name: '文件夹1',
		type: 'folder',
		createdAt: new Date('2024-01-01'),
		updatedAt: new Date('2024-01-01'),
	},
	{
		id: 'folder2',
		name: '文件夹2',
		type: 'folder',
		createdAt: new Date('2024-01-02'),
		updatedAt: new Date('2024-01-02'),
	},
];

const mockPages: PageItem[] = [
	{
		id: 'page1',
		name: '页面1',
		type: 'page',
		folderId: 'folder1',
		createdAt: new Date('2024-01-01'),
		updatedAt: new Date('2024-01-01'),
	},
	{
		id: 'page2',
		name: '页面2',
		type: 'page',
		folderId: 'folder1',
		createdAt: new Date('2024-01-02'),
		updatedAt: new Date('2024-01-02'),
	},
	{
		id: 'page3',
		name: '页面3',
		type: 'page',
		folderId: 'folder2',
		createdAt: new Date('2024-01-03'),
		updatedAt: new Date('2024-01-03'),
	},
	{
		id: 'page4',
		name: '页面4',
		type: 'page',
		createdAt: new Date('2024-01-04'),
		updatedAt: new Date('2024-01-04'),
	},
];

const Operation = () => {
	// 状态管理
	const [folders, setFolders] = useState<FolderItem[]>(mockFolders);
	const [pages, setPages] = useState<PageItem[]>(mockPages);
	const [searchValue, setSearchValue] = useState('');
	const [expandedKeys, setExpandedKeys] = useState<string[]>(['folder1', 'folder2']);

	// 弹窗状态
	const [createPageModalOpen, setCreatePageModalOpen] = useState(false);
	const [createPageType, setCreatePageType] = useState<'blank' | 'datasource'>('blank');
	const [createBlankPageModalOpen, setCreateBlankPageModalOpen] = useState(false);
	const [createDataSourcePageModalOpen, setCreateDataSourcePageModalOpen] = useState(false);
	const [createFolderModalOpen, setCreateFolderModalOpen] = useState(false);
	const [renameModalOpen, setRenameModalOpen] = useState(false);
	const [copyPageModalOpen, setCopyPageModalOpen] = useState(false);
	const [currentEditItem, setCurrentEditItem] = useState<PageItem | FolderItem | null>(null);

	// 表单实例
	const [createPageForm] = Form.useForm();
	const [createBlankPageForm] = Form.useForm();
	const [createDataSourcePageForm] = Form.useForm();
	const [createFolderForm] = Form.useForm();
	const [renameForm] = Form.useForm();
	const [copyPageForm] = Form.useForm();


	// 构建树形数据
	const buildTreeData = useMemo(() => {
		const filteredFolders = folders.filter(folder =>
			folder.name.toLowerCase().includes(searchValue.toLowerCase())
		);
		const filteredPages = pages.filter(page =>
			page.name.toLowerCase().includes(searchValue.toLowerCase())
		);

		const treeData: TreeNodeData[] = [];

		// 添加文件夹
		filteredFolders.forEach(folder => {
			const folderNode: TreeNodeData = {
				...folder,
				key: folder.id,
				title: folder.name,
				children: [],
			};

			// 添加文件夹下的页面
			const folderPages = filteredPages.filter(page => page.folderId === folder.id);
			folderPages.forEach(page => {
				folderNode.children!.push({
					...page,
					key: page.id,
					title: page.name,
				});
			});

			treeData.push(folderNode);
		});

		// 添加根级别的页面（没有文件夹的页面）
		const rootPages = filteredPages.filter(page => !page.folderId);
		rootPages.forEach(page => {
			treeData.push({
				...page,
				key: page.id,
				title: page.name,
			});
		});

		return treeData;
	}, [folders, pages, searchValue]);

	// 自定义树节点渲染
	const renderTreeNode = (nodeData: TreeNodeData) => {
		const isFolder = nodeData.type === 'folder';

		return (
			<div className="flex items-center justify-between group">
				<div className="flex items-center">
					{isFolder ? (
						<FolderOutlined className="mr-2 text-blue-500" />
					) : (
						<FileOutlined className="mr-2 text-gray-500" />
					)}
					<span>{nodeData.title}</span>
				</div>

				<div className="invisible group-hover:visible">
					{isFolder ? (
						<DeleteOutlined
							className="text-red-500 hover:text-red-700 cursor-pointer"
							onClick={(e) => {
								e.stopPropagation();
								handleDeleteFolder(nodeData.id);
							}}
						/>
					) : (
						<Dropdown
							menu={{
								items: [
									{
										key: 'rename',
										label: '重命名',
										icon: <EditOutlined />,
									},
									{
										key: 'copy',
										label: '复制页面',
										icon: <CopyOutlined />,
									},
									{
										key: 'delete',
										label: '删除',
										icon: <DeleteOutlined />,
										danger: true,
									},
								],
								onClick: ({ key }) => handlePageAction(nodeData as PageItem, key),
							}}
							trigger={['click']}
						>
							<MoreOutlined
								className="text-gray-500 hover:text-gray-700 cursor-pointer"
								onClick={(e) => e.stopPropagation()}
							/>
						</Dropdown>
					)}
				</div>
			</div>
		);
	};

	// 处理文件夹删除
	const handleDeleteFolder = (folderId: string) => {
		const folder = folders.find(f => f.id === folderId);
		const folderPages = pages.filter(p => p.folderId === folderId);
		Modal.confirm({
			title: '确认删除',
			icon: <ExclamationCircleOutlined />,
			content: `是否删除该文件夹"${folder?.name}"及文件夹下${folderPages.length}个页面？`,
			okText: '删除',
			okType: 'danger',
			cancelText: '取消',
			onOk() {
				setFolders(prev => prev.filter(f => f.id !== folderId));
				setPages(prev => prev.filter(p => p.folderId !== folderId));
				message.success('删除成功');
			},
		});
	};

	// 处理页面操作
	const handlePageAction = (page: PageItem, action: string) => {
		switch (action) {
			case 'rename':
				setCurrentEditItem(page);
				renameForm.setFieldsValue({ name: page.name });
				setRenameModalOpen(true);
				break;
			case 'copy':
				setCurrentEditItem(page);
				copyPageForm.setFieldsValue({
					name: `${page.name}_副本`,
					folderId: page.folderId
				});
				setCopyPageModalOpen(true);
				break;
			case 'delete':
				Modal.confirm({
					title: '确认删除',
					icon: <ExclamationCircleOutlined />,
					content: `是否删除该页面"${page.name}"？`,
					okText: '删除',
					okType: 'danger',
					cancelText: '取消',
					onOk() {
						setPages(prev => prev.filter(p => p.id !== page.id));
						message.success('删除成功');
					},
				});
				break;
		}
	};

	return (
		<div className="flex h-[calc(100vh-64px)] overflow-hidden">
			{/* 左侧操作面板 */}
			<div className="w-80 bg-white border-r border-gray-200 flex flex-col gap-2 px-2">
				{/* 左侧顶部 - 新建页面 */}
				<Button
					type="primary"
					icon={<PlusOutlined />}
					onClick={() => setCreatePageModalOpen(true)}
					className="w-full mt-2 border-b border-gray-100"
				>
					新建页面
				</Button>
				{/* 左侧中部 - 目录树 */}
				<Flex vertical gap={'small'} className={'flex-1 h-full'}>
					{/* <div className="mb-3"> */}
					<Input
						placeholder="搜索页面或文件夹"
						prefix={<SearchOutlined />}
						value={searchValue}
						onChange={(e) => setSearchValue(e.target.value)}
						size="small"
					/>
					{/* </div> */}
					<div className="flex flex-1 overflow-y-auto h-[calc(100vh-210px)]">
						{buildTreeData.length > 0 ? (
							<Tree
								treeData={buildTreeData}
								expandedKeys={expandedKeys}
								onExpand={(keys) => setExpandedKeys(keys as string[])}
								showLine={{ showLeafIcon: false }}
								blockNode
								titleRender={renderTreeNode}
							/>
						) : (
							<div className="text-center text-gray-500 mt-10 text-sm">
								{searchValue ? '未找到匹配的页面或文件夹' : '暂无页面，点击"新建页面"开始创建'}
							</div>
						)}
					</div>
					{/* 左侧底部 - 新建文件夹 */}
					<Button
						icon={<FolderOutlined />}
						onClick={() => setCreateFolderModalOpen(true)}
						className="w-full mt-4"
						type="text"
					>
						新建文件夹
					</Button>
				</Flex>
			</div>
			{/* 右侧内容区域 */}
			<div className="flex-1 flex flex-col">
				{/* 右侧顶部 - 编辑页面按钮 */}
				<div className="bg-white p-2">
					<div className="flex justify-end items-center">
						{/* <h2 className="text-xl font-semibold text-gray-800">页面内容</h2> */}
						<Button type="primary" icon={<EditOutlined />}>
							编辑页面
						</Button>
					</div>
				</div>

				{/* 右侧主内容区 - 页面内容占位 */}
				<div className="flex-1 p-2">
					<div className="h-full bg-white rounded-lg shadow-sm border-2 border-dashed border-gray-200 flex items-center justify-center">
						<div className="text-center text-gray-400">
							<FileOutlined className="text-4xl mb-4" />
							<div className="text-lg mb-2">页面内容区域</div>
							<div className="text-sm">选择左侧页面查看具体内容</div>
						</div>
					</div>
				</div>
			</div>

			{/* 新建页面弹窗 */}
			<Modal
				title="新建页面"
				open={createPageModalOpen}
				onCancel={() => {
					setCreatePageModalOpen(false);
					createPageForm.resetFields();
				}}
				footer={null}
				width={500}
			>
				<div className="space-y-4">
					<div className="text-sm text-gray-600 mb-4">请选择页面创建方式：</div>
					<div className="grid grid-cols-2 gap-4">
						<div
							className={`border-2 rounded-lg p-4 cursor-pointer transition-colors ${createPageType === 'blank'
								? 'border-blue-500 bg-blue-50'
								: 'border-gray-200 hover:border-gray-300'
								}`}
							onClick={() => setCreatePageType('blank')}
						>
							<div className="text-center">
								<FileOutlined className="text-2xl mb-2 text-gray-400" />
								<div className="font-medium">空白页面</div>
								<div className="text-xs text-gray-500 mt-1">创建一个空白页面</div>
							</div>
						</div>
						<div
							className={`border-2 rounded-lg p-4 cursor-pointer transition-colors ${createPageType === 'datasource'
								? 'border-blue-500 bg-blue-50'
								: 'border-gray-200 hover:border-gray-300'
								}`}
							onClick={() => setCreatePageType('datasource')}
						>
							<div className="text-center">
								<FileOutlined className="text-2xl mb-2 text-blue-400" />
								<div className="font-medium">从数据源创建</div>
								<div className="text-xs text-gray-500 mt-1">基于数据源创建页面</div>
							</div>
						</div>
					</div>
					<div className="flex justify-end space-x-2 pt-4">
						<Button onClick={() => setCreatePageModalOpen(false)}>
							取消
						</Button>
						<Button
							type="primary"
							onClick={() => {
								if (createPageType === 'blank') {
									setCreatePageModalOpen(false);
									setCreateBlankPageModalOpen(true);
								} else {
									setCreatePageModalOpen(false);
									setCreateDataSourcePageModalOpen(true);
								}
							}}
						>
							下一步
						</Button>
					</div>
				</div>
			</Modal>

			{/* 新建文件夹弹窗 */}
			<Modal
				title="新建文件夹"
				open={createFolderModalOpen}
				onOk={() => {
					createFolderForm.validateFields().then(values => {
						const newFolder: FolderItem = {
							id: nanoid(),
							name: values.name,
							type: 'folder',
							createdAt: new Date(),
							updatedAt: new Date(),
						};
						setFolders(prev => [...prev, newFolder]);
						setCreateFolderModalOpen(false);
						createFolderForm.resetFields();
						message.success('文件夹创建成功');
					});
				}}
				onCancel={() => {
					setCreateFolderModalOpen(false);
					createFolderForm.resetFields();
				}}
				okText="确定"
				cancelText="取消"
			>
				<Form form={createFolderForm} layout="vertical">
					<Form.Item
						label="文件夹名称"
						name="name"
						rules={[{ required: true, message: '请输入文件夹名称' }]}
					>
						<Input placeholder="请输入文件夹名称" />
					</Form.Item>
				</Form>
			</Modal>

			{/* 重命名弹窗 */}
			<Modal
				title="重命名"
				open={renameModalOpen}
				onOk={() => {
					renameForm.validateFields().then(values => {
						if (currentEditItem) {
							if (currentEditItem.type === 'folder') {
								setFolders(prev => prev.map(f =>
									f.id === currentEditItem.id
										? { ...f, name: values.name, updatedAt: new Date() }
										: f
								));
							} else {
								setPages(prev => prev.map(p =>
									p.id === currentEditItem.id
										? { ...p, name: values.name, updatedAt: new Date() }
										: p
								));
							}
							setRenameModalOpen(false);
							renameForm.resetFields();
							setCurrentEditItem(null);
							message.success('重命名成功');
						}
					});
				}}
				onCancel={() => {
					setRenameModalOpen(false);
					renameForm.resetFields();
					setCurrentEditItem(null);
				}}
				okText="确定"
				cancelText="取消"
			>
				<Form form={renameForm} layout="vertical">
					<Form.Item
						label="名称"
						name="name"
						rules={[{ required: true, message: '请输入名称' }]}
					>
						<Input placeholder="请输入名称" />
					</Form.Item>
				</Form>
			</Modal>

			{/* 复制页面弹窗 */}
			<Modal
				title="复制页面"
				open={copyPageModalOpen}
				onOk={() => {
					copyPageForm.validateFields().then(values => {
						if (currentEditItem && currentEditItem.type === 'page') {
							const newPage: PageItem = {
								id: nanoid(),
								name: values.name,
								type: 'page',
								folderId: values.folderId,
								createdAt: new Date(),
								updatedAt: new Date(),
							};
							setPages(prev => [...prev, newPage]);
							setCopyPageModalOpen(false);
							copyPageForm.resetFields();
							setCurrentEditItem(null);
							message.success('页面复制成功');
						}
					});
				}}
				onCancel={() => {
					setCopyPageModalOpen(false);
					copyPageForm.resetFields();
					setCurrentEditItem(null);
				}}
				okText="确定"
				cancelText="取消"
			>
				<Form form={copyPageForm} layout="vertical">
					<Form.Item
						label="页面名称"
						name="name"
						rules={[{ required: true, message: '请输入页面名称' }]}
					>
						<Input placeholder="请输入页面名称" />
					</Form.Item>
					<Form.Item
						label="存储位置"
						name="folderId"
					>
						<Select placeholder="请选择存储位置" allowClear>
							{folders.map(folder => (
								<Select.Option key={folder.id} value={folder.id}>
									{folder.name}
								</Select.Option>
							))}
						</Select>
					</Form.Item>
				</Form>
			</Modal>

			{/* 新建空白页面弹窗 */}
			<Modal
				title="新建空白页面"
				open={createBlankPageModalOpen}
				onOk={() => {
					createBlankPageForm.validateFields().then(values => {
						const newPage: PageItem = {
							id: nanoid(),
							name: values.name,
							type: 'page',
							folderId: values.folderId,
							createdAt: new Date(),
							updatedAt: new Date(),
						};
						setPages(prev => [...prev, newPage]);
						setCreateBlankPageModalOpen(false);
						createBlankPageForm.resetFields();
						message.success('空白页面创建成功');
					});
				}}
				onCancel={() => {
					setCreateBlankPageModalOpen(false);
					createBlankPageForm.resetFields();
				}}
				okText="确定"
				cancelText="取消"
			>
				<Form form={createBlankPageForm} layout="vertical">
					<Form.Item
						label="页面名称"
						name="name"
						rules={[{ required: true, message: '请输入页面名称' }]}
						initialValue="请输入页面名称"
					>
						<Input placeholder="请输入页面名称" />
					</Form.Item>
					<Form.Item
						label="存储位置"
						name="folderId"
					>
						<Select placeholder="请选择存储位置" allowClear>
							{folders.map(folder => (
								<Select.Option key={folder.id} value={folder.id}>
									{folder.name}
								</Select.Option>
							))}
						</Select>
					</Form.Item>
				</Form>
			</Modal>

			{/* 从数据源创建页面弹窗 */}
			<Modal
				title="从数据源创建"
				open={createDataSourcePageModalOpen}
				onOk={() => {
					createDataSourcePageForm.validateFields().then(values => {
						const newPage: PageItem = {
							id: nanoid(),
							name: values.name,
							type: 'page',
							folderId: values.folderId,
							createdAt: new Date(),
							updatedAt: new Date(),
						};
						setPages(prev => [...prev, newPage]);
						setCreateDataSourcePageModalOpen(false);
						createDataSourcePageForm.resetFields();
						message.success('页面创建成功');
					});
				}}
				onCancel={() => {
					setCreateDataSourcePageModalOpen(false);
					createDataSourcePageForm.resetFields();
				}}
				okText="确定"
				cancelText="取消"
			>
				<Form form={createDataSourcePageForm} layout="vertical">
					<Form.Item
						label="页面名称"
						name="name"
						rules={[{ required: true, message: '请输入页面名称' }]}
						initialValue="请输入页面名称"
					>
						<Input placeholder="请输入页面名称" />
					</Form.Item>
					<Form.Item
						label="存储位置"
						name="folderId"
					>
						<Select placeholder="请选择存储位置" allowClear>
							{folders.map(folder => (
								<Select.Option key={folder.id} value={folder.id}>
									{folder.name}
								</Select.Option>
							))}
						</Select>
					</Form.Item>
				</Form>
				<div className="text-xs text-gray-400 mt-2">
					选择创建页面存储位置，若不选择文件夹，则默认当前文件夹，
					单独以输入文件名，点击确认，直接保存在该文件夹
				</div>
			</Modal>
		</div>
	);
};

export default Operation;

