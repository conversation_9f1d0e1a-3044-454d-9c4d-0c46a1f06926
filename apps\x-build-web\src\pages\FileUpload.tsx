import { useState, useCallback, useMemo } from 'react';
import {
	Button,
	Upload,
	Input,
	Modal,
	Form,
	message,
	Space
} from 'antd';
import {
	UploadOutlined,
	EyeOutlined,
	// CheckCircleOutlined,
	ExclamationCircleOutlined,
	LoadingOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useMutation } from '@tanstack/react-query';
import { trpc, queryClient } from '../utils/tRPC';
import axios from 'axios'
import { FieldInfo, TableInfo } from '../types';
import TableStructureEditor from '../components/TableStructureEditor';
import FileDataPreview from '../components/FileDataPreview';
import FileUploadList from '../components/FileUploadList';

const FileUpload = () => {
	const navigate = useNavigate();
	const [tables, setTables] = useState<TableInfo[]>([]);
	const [selectedTableId, setSelectedTableId] = useState<string>('');
	const [aliasModalOpen, setAliasModalOpen] = useState(false);
	const [primaryKeyModalOpen, setPrimaryKeyModalOpen] = useState(false);
	const [currentEditTable, setCurrentEditTable] = useState<TableInfo | null>(null);
	const [uploading, setUploading] = useState(false);
	const [uploadingFiles, setUploadingFiles] = useState<Set<string>>(new Set());
	const [fileProgress, setFileProgress] = useState<Map<string, number>>(new Map());
	const [aliasForm] = Form.useForm();

	// 字段类型推断工具函数
	const inferFieldType = useCallback((sampleValues: string[]): FieldInfo['type'] => {
		if (sampleValues.length === 0) return 'text';

		// 检查是否全部为数字
		const allNumbers = sampleValues.every(val => !isNaN(Number(val)) && val !== '');
		if (allNumbers) {
			// 检查是否包含小数点
			const hasDecimals = sampleValues.some(val => val.includes('.'));
			return hasDecimals ? 'decimal' : 'integer';
		}

		// 检查是否为日期格式（排除纯数字）
		const allDates = sampleValues.every(val => {
			const date = new Date(val);
			return !isNaN(date.getTime()) && val.length > 4; // 排除年份等短数字
		});

		if (allDates) {
			return 'date';
		}

		return 'text';
	}, []);

	// 创建字段信息
	const createFieldsInfo = useCallback((data: Record<string, any>[], headers: string[]): FieldInfo[] => {
		return headers.map(header => {
			// 收集前10个非空值作为样本
			const sampleValues: string[] = [];

			for (const row of data.slice(0, 10)) {
				const value = row[header];
				if (value && value.toString().trim()) {
					sampleValues.push(value.toString().trim());
					if (sampleValues.length >= 5) break; // 最多分析5个样本
				}
			}

			const inferredType = inferFieldType(sampleValues);

			return {
				name: header,
				originalName: header,
				type: inferredType,
				description: ''
			};
		});
	}, [inferFieldType]);



	// 数据转换工具函数：将后端API返回的数据转换为TableInfo格式
	const convertBackendDataToTableInfo = useCallback((
		backendData: { totalCount: number; records: Record<string, any>[] },
		fileName: string,
		file: File,
		fileKey: string
	): TableInfo => {
		// 处理空数据情况
		if (!backendData.records || backendData.records.length === 0) {
			return {
				id: fileKey,
				fileName,
				alias: fileName.replace(/\.[^/.]+$/, ''), // 移除文件扩展名作为默认别名
				file,
				fields: [],
				data: [],
				selected: false,
				uploadStatus: 'success'
			};
		}

		// 提取字段名称（从第一条记录获取）
		const firstRecord = backendData.records[0];
		const headers = Object.keys(firstRecord);

		// 使用字段类型推断函数生成字段信息
		const fields = createFieldsInfo(backendData.records, headers);

		// 转换数据格式，为每条记录添加key字段（Ant Design Table需要）
		const data = backendData.records.map((record, index) => ({
			key: index,
			...record
		}));

		return {
			id: fileKey,
			fileName,
			alias: fileName.replace(/\.[^/.]+$/, ''), // 移除文件扩展名作为默认别名
			file,
			fields,
			data,
			selected: false,
			uploadStatus: 'success'
		};
	}, [createFieldsInfo]);



	// tRPC hooks
	const getPresignedUrlMutation = useMutation({
		...trpc.upload.getPresignedUrl.mutationOptions(),
		onError: (error) => {
			console.error('获取预签名URL失败:', error);
			message.error('获取上传链接失败，请重试');
		}
	});

	// 文件上传函数 - 参考您提供的代码结构
	const uploadFile = useCallback(async (file: File): Promise<{ key: string }> => {
		// 获取预签名URL
		const { url, key } = await getPresignedUrlMutation.mutateAsync({
			fileName: file.name,
			fileType: file.type || 'text/csv',
		});

		// 上传文件到S3
		const response = await axios.put(url, file, {
			headers: {
				'Content-Type': file.type || 'text/csv'
			},
		});


		if (response.status !== 200) {
			throw new Error(`S3上传失败: ${response.status} ${response.statusText}`);
		}

		// console.log('上传成功:', file.name);
		return { key };
	}, [getPresignedUrlMutation]);


	// 文件上传前处理
	const handleBeforeUpload = useCallback(async (file: File) => {
		// 基本文件验证
		if (!file.name.toLowerCase().endsWith('.csv')) {
			message.error('只支持CSV文件格式');
			return false;
		}

		if (file.size > 100 * 1024 * 1024) {
			message.error('文件大小不能超过100MB');
			return false;
		}

		const fileId = `${Date.now()}-${file.name}`;

		try {
			// 设置上传状态
			setUploading(true);
			setUploadingFiles(prev => new Set(prev).add(fileId));
			setFileProgress(prev => new Map(prev).set(fileId, 0));

			// 步骤1: 上传文件 (包含获取预签名URL和上传到S3)
			setFileProgress(prev => new Map(prev).set(fileId, 25));
			const { key } = await uploadFile(file);
			setFileProgress(prev => new Map(prev).set(fileId, 75));

			// 步骤2: 获取预览数据
			const previewData = await queryClient.fetchQuery({
				...trpc.dataSource.previewUploadFile.queryOptions({
					fileKey: `xbuild-test/${key}`,
					page: 1,
					pageSize: 100
				})
			});

			// 步骤3: 转换数据格式
			const tableInfo = convertBackendDataToTableInfo(
				previewData,
				file.name,
				file,
				key
			);

			// 步骤4: 更新状态 (100%进度)
			setFileProgress(prev => new Map(prev).set(fileId, 100));
			setTables(prev => [...prev, tableInfo]);

			// 如果是第一个文件，自动选中
			if (tables.length === 0) {
				setSelectedTableId(tableInfo.id);
			}

			message.success(`文件 ${file.name} 上传成功`);

		} catch (error) {
			console.error('文件上传失败:', error);
			message.error(`文件上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
		} finally {
			// 清理上传状态
			setUploadingFiles(prev => {
				const newSet = new Set(prev);
				newSet.delete(fileId);
				return newSet;
			});
			setFileProgress(prev => {
				const newMap = new Map(prev);
				newMap.delete(fileId);
				return newMap;
			});

			// 如果没有其他文件在上传，设置uploading为false
			setUploadingFiles(current => {
				if (current.size === 0) {
					setUploading(false);
				}
				return current;
			});
		}

		return false; // 阻止Ant Design的默认上传行为
	}, [uploadFile, convertBackendDataToTableInfo, tables.length]);

	// 删除表 - 使用useCallback优化
	const handleDeleteTable = useCallback((tableId: string) => {
		setTables(prev => prev.filter(t => t.id !== tableId));
		if (selectedTableId === tableId) {
			setSelectedTableId('');
		}
	}, [selectedTableId]);

	// 取消文件解析 - 使用useCallback优化
	const handleCancelParse = useCallback((fileId: string) => {
		setUploadingFiles(prev => {
			const newSet = new Set(prev);
			newSet.delete(fileId);
			// 如果没有其他文件在解析，清除全局loading状态
			if (newSet.size === 0) {
				setUploading(false);
			}
			return newSet;
		});
		setFileProgress(prev => {
			const newMap = new Map(prev);
			newMap.delete(fileId);
			return newMap;
		});

		message.info('已取消文件解析');
	}, []);

	// 编辑表别名 - 使用useCallback优化
	const handleEditAlias = useCallback((table: TableInfo) => {
		setCurrentEditTable(table);
		aliasForm.setFieldsValue({ alias: table.alias });
		setAliasModalOpen(true);
	}, [aliasForm]);

	// 保存别名 - 使用useCallback优化
	const handleSaveAlias = useCallback(() => {
		aliasForm.validateFields().then(values => {
			if (currentEditTable) {
				setTables(prev => prev.map(t =>
					t.id === currentEditTable.id
						? { ...t, alias: values.alias }
						: t
				));
				setAliasModalOpen(false);
				setCurrentEditTable(null);
				aliasForm.resetFields();
				message.success('别名修改成功');
			}
		});
	}, [aliasForm, currentEditTable]);

	// 字段更新回调 - 使用useCallback优化
	const handleFieldUpdate = useCallback((tableId: string, fieldIndex: number, field: string, value: any) => {
		setTables(prev => prev.map(t => {
			if (t.id === tableId) {
				const newFields = [...t.fields];
				(newFields[fieldIndex] as any)[field] = value;
				return { ...t, fields: newFields };
			}
			return t;
		}));
	}, []);

	// 表格选择回调 - 使用useCallback优化
	const handleTableSelect = useCallback((tableId: string) => {
		setSelectedTableId(tableId);
	}, []);

	// 表格切换回调 - 使用useCallback优化
	const handleTableToggle = useCallback((tableId: string, selected: boolean) => {
		setTables(prev => prev.map(t =>
			t.id === tableId
				? { ...t, selected }
				: t
		));
	}, []);

	// 设置主键回调 - 使用useCallback优化
	const handleSetPrimaryKey = useCallback((table: TableInfo) => {
		setCurrentEditTable(table);
		setPrimaryKeyModalOpen(true);
	}, []);

	// 获取当前选中的表 - 使用useMemo缓存
	const selectedTable = useMemo(() => {
		return tables.find(t => t.id === selectedTableId);
	}, [tables, selectedTableId]);

	return (
		<div className="h-[calc(100vh-64px)] flex overflow-hidden">
			{/* 左侧上传列表 */}
			<div className="w-64 border-r flex flex-col">
				{/* 顶部上传区域 */}
				<div className="p-4 border-b border-gray-100">
					<Upload
						beforeUpload={handleBeforeUpload}
						showUploadList={false}
						accept=".csv"
						multiple
					>
						<Button
							icon={uploading ? <LoadingOutlined /> : <UploadOutlined />}
							className="w-full"
							loading={uploading}
							disabled={uploading}
						>
							{uploading ? '解析中...' : '上传CSV文件'}
						</Button>
					</Upload>
				</div>

				{/* 文件列表 */}
				<FileUploadList
					tables={tables}
					selectedTableId={selectedTableId}
					uploadingFiles={uploadingFiles}
					fileProgress={fileProgress}
					onTableSelect={handleTableSelect}
					onTableToggle={handleTableToggle}
					onDeleteTable={handleDeleteTable}
					onCancelParse={handleCancelParse}
					onEditAlias={handleEditAlias}
					onSetPrimaryKey={handleSetPrimaryKey}
				/>

				{/* 底部操作按钮 */}
				<div className="p-4 border-t border-gray-100 space-y-2">
					<Button
						type="primary"
						className="w-full"
						disabled={!tables.some(t => t.selected) || uploading}
						loading={uploading}
						onClick={() => setPrimaryKeyModalOpen(true)}
					>
						{uploading ? '解析中...' : `确定上传 (${tables.filter(t => t.selected).length})`}
					</Button>
					<Button
						className="w-full"
						onClick={() => navigate(-1)}
					>
						返回
					</Button>
				</div>
			</div>

			{/* 右侧预览区域 */}
			<div className="flex-1 flex flex-col">
				{selectedTable ? (
					<>
						{/* 顶部标题 */}
						<div className="p-2 border-b border-gray-200">
							<div className="flex items-center justify-between">
								<Space>
									<div className="text-lg font-semibold">{selectedTable.alias}</div>
									<div className="text-sm text-gray-500">{selectedTable.fileName}</div>
								</Space>
							</div>
						</div>
						{/* 内容区域 - 左右两栏布局 */}
						<div className="grid grid-cols-[3fr_auto_5fr]">
							<TableStructureEditor
								table={selectedTable}
								onFieldUpdate={handleFieldUpdate}
							/>
							<div className="border-l border-gray-200"></div>
							<FileDataPreview table={selectedTable} />
						</div>
					</>
				) : (
					<div className="flex-1 flex items-center justify-center">
						<div className="text-center text-gray-400">
							<EyeOutlined className="text-4xl mb-4" />
							<div className="text-lg mb-2">选择文件查看预览</div>
							<div className="text-sm">请从左侧选择已上传的CSV文件</div>
						</div>
					</div>
				)}
			</div>

			{/* 编辑别名弹窗 */}
			<Modal
				title="编辑表别名"
				open={aliasModalOpen}
				onOk={handleSaveAlias}
				onCancel={() => {
					setAliasModalOpen(false);
					setCurrentEditTable(null);
					aliasForm.resetFields();
				}}
				okText="确定"
				cancelText="取消"
			>
				<Form form={aliasForm} layout="vertical">
					<Form.Item
						label="表别名"
						name="alias"
						rules={[{ required: true, message: '请输入表别名' }]}
					>
						<Input placeholder="请输入表别名" />
					</Form.Item>
				</Form>
			</Modal >

			{/* 主键设置确认弹窗 */}
			<Modal
				title="上传确认"
				open={primaryKeyModalOpen}
				onOk={() => {
					// 处理上传逻辑
					setPrimaryKeyModalOpen(false);
					message.success('上传成功');
					navigate(-1);
				}}
				onCancel={() => setPrimaryKeyModalOpen(false)}
				okText="确定"
				cancelText="取消"
			>
				<div className="space-y-4">
					<div className="flex items-center space-x-2">
						<ExclamationCircleOutlined className="text-orange-500" />
						<span>是否需要为上传的表自动设置主键？</span>
					</div>
					<div className="text-sm text-gray-500">
						系统将为每个表自动添加一个自增主键字段，用于唯一标识每条记录。
					</div>
				</div>
			</Modal>
		</div >
	);
};

export default FileUpload;
